# ArchPDF Installation Guide

## System Requirements

- **Operating System**: Windows 10 or later (64-bit)
- **Memory**: 4 GB RAM minimum, 8 GB recommended
- **Storage**: 100 MB free disk space
- **Display**: 1024x768 minimum resolution

## Installation Options

### Option 1: Standalone Executable (Recommended)

1. Download `ArchPDF.exe` from the releases
2. Place the executable in your desired location
3. Double-click to run - no installation required!

### Option 2: Run from Source

#### Prerequisites

- Python 3.8 or higher
- pip package manager

#### Installation Steps

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd archpdf_v2
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python main.py
   ```

#### Building Your Own Executable

1. **Install PyInstaller**
   ```bash
   pip install pyinstaller
   ```

2. **Build the executable**
   ```bash
   python build_exe.py
   ```

3. **Find the executable**
   The built executable will be in the `dist/` folder.

## First Run

1. Launch ArchPDF
2. Use **File > Open** to select a PDF document
3. Navigate using the toolbar buttons or keyboard shortcuts
4. Explore the sidebar for bookmarks and thumbnails

## Configuration

ArchPDF stores its configuration in:
- **Windows**: `%USERPROFILE%\.archpdf\`

Configuration includes:
- User preferences
- Recent files list
- Window layout settings
- Application logs

## Troubleshooting

### Common Issues

**Application won't start**
- Ensure you have the latest Visual C++ Redistributable installed
- Check Windows Event Viewer for error details
- Try running as administrator

**PDF won't open**
- Verify the file is a valid PDF
- Check file permissions
- Try opening a different PDF file

**Performance issues**
- Close other applications to free memory
- Reduce zoom level for large documents
- Restart the application

**Missing features**
- Ensure you're using the latest version
- Check that all dependencies are installed (source installation)

### Getting Help

1. Check the logs in `%USERPROFILE%\.archpdf\logs\`
2. Review the user guide in `USER_GUIDE.md`
3. Report issues with detailed error information

## Uninstallation

### Standalone Executable
1. Delete the `ArchPDF.exe` file
2. Optionally delete the configuration folder: `%USERPROFILE%\.archpdf\`

### Source Installation
1. Delete the project folder
2. Uninstall Python packages (if not needed elsewhere):
   ```bash
   pip uninstall customtkinter PyMuPDF Pillow
   ```
3. Optionally delete the configuration folder: `%USERPROFILE%\.archpdf\`

## Security Notes

- ArchPDF only reads PDF files and doesn't execute any code from them
- No network connections are made by the application
- All data is stored locally on your computer
- The application doesn't collect or transmit any personal information
