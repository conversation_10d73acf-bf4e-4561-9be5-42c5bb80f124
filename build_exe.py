#!/usr/bin/env python3
"""
Build script for creating standalone ArchPDF executable using PyInstaller.
"""

import os
import sys
import shutil
from pathlib import Path
import PyInstaller.__main__

def clean_build():
    """Clean previous build artifacts."""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"Cleaned {dir_name}/")

def build_executable():
    """Build the standalone executable."""
    print("Building ArchPDF standalone executable...")

    # Check if icon exists
    icon_path = Path('assets/icons/app_icon.ico')
    if not icon_path.exists():
        print("Warning: App icon not found, creating it...")
        os.system('python create_app_icon.py')

    # Check if spec file exists
    spec_file = Path('ArchPDF.spec')
    if spec_file.exists():
        print("Using custom spec file...")
        args = [str(spec_file), '--clean', '--noconfirm']
    else:
        print("Using direct PyInstaller arguments...")
        # Fallback to direct arguments
        args = [
            'main.py',
            '--name=ArchPDF',
            '--onefile',
            '--windowed',
            '--hidden-import=tkinter',
            '--hidden-import=customtkinter',
            '--hidden-import=fitz',
            '--hidden-import=PIL',
            '--collect-all=customtkinter',
            '--distpath=dist',
            '--workpath=build',
            '--clean',
            '--noconfirm'
        ]

        # Add icon if it exists
        if icon_path.exists():
            args.append(f'--icon={icon_path}')

    # Run PyInstaller
    PyInstaller.__main__.run(args)

    print("Build completed!")
    print(f"Executable created: {Path('dist/ArchPDF.exe').absolute()}")

    # Check executable size
    exe_path = Path('dist/ArchPDF.exe')
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"Executable size: {size_mb:.1f} MB")

def main():
    """Main build function."""
    print("ArchPDF Build Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path('main.py').exists():
        print("Error: main.py not found. Please run this script from the project root.")
        sys.exit(1)
    
    # Clean previous builds
    clean_build()
    
    # Build executable
    try:
        build_executable()
        print("\nBuild successful! 🎉")
        print("You can find the executable in the 'dist' folder.")
    except Exception as e:
        print(f"\nBuild failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
