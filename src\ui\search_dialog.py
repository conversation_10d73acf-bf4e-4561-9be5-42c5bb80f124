"""
Search dialog for ArchPDF - Text search functionality with advanced options.
"""

import tkinter as tk
import customtkinter as ctk
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class SearchDialog(ctk.CTkToplevel):
    """Search dialog for finding text in PDF documents."""
    
    def __init__(self, parent, app):
        """
        Initialize the search dialog.
        
        Args:
            parent: Parent window
            app: Main application instance
        """
        super().__init__(parent)
        self.app = app
        self.search_engine = app.search_engine
        
        # Configure window
        self.title("Find Text")
        self.geometry("400x300")
        self.resizable(False, False)
        
        # Make dialog modal
        self.transient(parent)
        self.grab_set()
        
        # Center on parent
        self._center_on_parent(parent)
        
        # Create UI
        self._create_ui()
        
        # Focus on search entry
        self.search_entry.focus()
        
        # Bind events
        self._bind_events()
    
    def _center_on_parent(self, parent):
        """Center dialog on parent window."""
        parent.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 200
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 150
        self.geometry(f"400x300+{x}+{y}")
    
    def _create_ui(self):
        """Create the search dialog UI."""
        # Main frame
        main_frame = ctk.CTkFrame(self, **self.app.theme_manager.get_frame_style("primary"))
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Search input section
        search_frame = ctk.CTkFrame(main_frame, **self.app.theme_manager.get_frame_style("secondary"))
        search_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        # Search label
        search_label = ctk.CTkLabel(
            search_frame,
            text="Find:",
            **self.app.theme_manager.get_label_style("primary")
        )
        search_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        # Search entry
        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="Enter text to search...",
            **self.app.theme_manager.get_entry_style()
        )
        self.search_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        # Options section
        options_frame = ctk.CTkFrame(main_frame, **self.app.theme_manager.get_frame_style("secondary"))
        options_frame.pack(fill="x", padx=10, pady=5)
        
        options_label = ctk.CTkLabel(
            options_frame,
            text="Options:",
            **self.app.theme_manager.get_label_style("primary")
        )
        options_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        # Checkboxes
        self.case_sensitive_var = tk.BooleanVar()
        self.case_sensitive_cb = ctk.CTkCheckBox(
            options_frame,
            text="Case sensitive",
            variable=self.case_sensitive_var,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.case_sensitive_cb.pack(anchor="w", padx=20, pady=2)
        
        self.whole_words_var = tk.BooleanVar()
        self.whole_words_cb = ctk.CTkCheckBox(
            options_frame,
            text="Whole words only",
            variable=self.whole_words_var,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.whole_words_cb.pack(anchor="w", padx=20, pady=2)
        
        self.regex_var = tk.BooleanVar()
        self.regex_cb = ctk.CTkCheckBox(
            options_frame,
            text="Regular expression",
            variable=self.regex_var,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.regex_cb.pack(anchor="w", padx=20, pady=(2, 10))
        
        # Results section
        results_frame = ctk.CTkFrame(main_frame, **self.app.theme_manager.get_frame_style("secondary"))
        results_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        results_label = ctk.CTkLabel(
            results_frame,
            text="Results:",
            **self.app.theme_manager.get_label_style("primary")
        )
        results_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        # Results info
        self.results_info_label = ctk.CTkLabel(
            results_frame,
            text="No search performed",
            **self.app.theme_manager.get_label_style("tertiary")
        )
        self.results_info_label.pack(anchor="w", padx=20, pady=2)
        
        # Navigation buttons frame
        nav_frame = ctk.CTkFrame(results_frame, **self.app.theme_manager.get_frame_style("transparent"))
        nav_frame.pack(fill="x", padx=10, pady=5)
        
        self.prev_result_btn = ctk.CTkButton(
            nav_frame,
            text="Previous",
            width=80,
            command=self._previous_result,
            state="disabled",
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.prev_result_btn.pack(side="left", padx=(0, 5))
        
        self.next_result_btn = ctk.CTkButton(
            nav_frame,
            text="Next",
            width=80,
            command=self._next_result,
            state="disabled",
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.next_result_btn.pack(side="left", padx=5)
        
        # Button section
        button_frame = ctk.CTkFrame(main_frame, **self.app.theme_manager.get_frame_style("transparent"))
        button_frame.pack(fill="x", padx=10, pady=(5, 10))
        
        # Buttons
        self.search_btn = ctk.CTkButton(
            button_frame,
            text="Find All",
            command=self._perform_search,
            **self.app.theme_manager.get_button_style("primary")
        )
        self.search_btn.pack(side="left", padx=(0, 5))
        
        self.clear_btn = ctk.CTkButton(
            button_frame,
            text="Clear",
            command=self._clear_search,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.clear_btn.pack(side="left", padx=5)
        
        self.close_btn = ctk.CTkButton(
            button_frame,
            text="Close",
            command=self.destroy,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.close_btn.pack(side="right")
    
    def _bind_events(self):
        """Bind keyboard events."""
        self.search_entry.bind("<Return>", lambda e: self._perform_search())
        self.bind("<Escape>", lambda e: self.destroy())
        
        # Protocol for window close
        self.protocol("WM_DELETE_WINDOW", self.destroy)
    
    def _perform_search(self):
        """Perform text search."""
        query = self.search_entry.get().strip()
        
        if not query:
            self.results_info_label.configure(text="Please enter search text")
            return
        
        if not self.app.pdf_engine.is_document_loaded():
            self.results_info_label.configure(text="No document loaded")
            return
        
        # Get search options
        case_sensitive = self.case_sensitive_var.get()
        whole_words = self.whole_words_var.get()
        regex = self.regex_var.get()
        
        # Perform search
        try:
            results = self.search_engine.search(query, case_sensitive, whole_words, regex)
            
            if results:
                count = len(results)
                current = self.search_engine.get_current_result_index()
                self.results_info_label.configure(text=f"Found {count} results (showing {current}/{count})")
                
                # Enable navigation buttons
                self.prev_result_btn.configure(state="normal")
                self.next_result_btn.configure(state="normal")
                
                # Go to first result
                self._go_to_current_result()
                
            else:
                self.results_info_label.configure(text="No results found")
                self.prev_result_btn.configure(state="disabled")
                self.next_result_btn.configure(state="disabled")
                
        except Exception as e:
            logger.error(f"Search failed: {e}")
            self.results_info_label.configure(text=f"Search error: {str(e)}")
    
    def _previous_result(self):
        """Go to previous search result."""
        result = self.search_engine.get_previous_result()
        if result:
            self._go_to_current_result()
            self._update_results_info()
    
    def _next_result(self):
        """Go to next search result."""
        result = self.search_engine.get_next_result()
        if result:
            self._go_to_current_result()
            self._update_results_info()
    
    def _go_to_current_result(self):
        """Navigate to the current search result."""
        result = self.search_engine.get_current_result()
        if result:
            page_num = result['page'] + 1  # Convert to 1-based
            self.app.go_to_page(page_num)
            
            # Update viewer to highlight search results
            self.app.pdf_viewer.update_page()
    
    def _update_results_info(self):
        """Update results information display."""
        count = self.search_engine.get_result_count()
        current = self.search_engine.get_current_result_index()
        
        if count > 0:
            self.results_info_label.configure(text=f"Found {count} results (showing {current}/{count})")
        else:
            self.results_info_label.configure(text="No results found")
    
    def _clear_search(self):
        """Clear search results."""
        self.search_entry.delete(0, "end")
        self.search_engine.clear_search()
        self.results_info_label.configure(text="No search performed")
        self.prev_result_btn.configure(state="disabled")
        self.next_result_btn.configure(state="disabled")
        
        # Clear highlights in viewer
        self.app.pdf_viewer.update_page()
