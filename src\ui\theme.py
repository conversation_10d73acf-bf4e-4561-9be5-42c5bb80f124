"""
Theme management for ArchPDF - Professional dark theme implementation.
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
from typing import Dict, Any
from app.config import DARK_THEME

class ThemeManager:
    """Manages application themes and styling."""
    
    def __init__(self, root: ctk.CTk):
        """
        Initialize theme manager.
        
        Args:
            root: Main application window
        """
        self.root = root
        self.current_theme = "dark"
        self.colors = DARK_THEME.copy()
        
        # Configure CustomTkinter theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Create custom styles
        self._create_custom_styles()
    
    def _create_custom_styles(self):
        """Create custom styles for the application."""
        # Configure CustomTkinter colors
        ctk.ThemeManager.theme["CTk"]["fg_color"] = [self.colors["bg_primary"], self.colors["bg_primary"]]
        ctk.ThemeManager.theme["CTkFrame"]["fg_color"] = [self.colors["bg_secondary"], self.colors["bg_secondary"]]
        ctk.ThemeManager.theme["CTkButton"]["fg_color"] = [self.colors["accent_primary"], self.colors["accent_primary"]]
        ctk.ThemeManager.theme["CTkButton"]["hover_color"] = [self.colors["hover"], self.colors["hover"]]
        ctk.ThemeManager.theme["CTkButton"]["text_color"] = [self.colors["fg_primary"], self.colors["fg_primary"]]
        
        # Configure entry widgets
        ctk.ThemeManager.theme["CTkEntry"]["fg_color"] = [self.colors["bg_tertiary"], self.colors["bg_tertiary"]]
        ctk.ThemeManager.theme["CTkEntry"]["border_color"] = [self.colors["border"], self.colors["border"]]
        ctk.ThemeManager.theme["CTkEntry"]["text_color"] = [self.colors["fg_primary"], self.colors["fg_primary"]]
        
        # Configure labels
        ctk.ThemeManager.theme["CTkLabel"]["text_color"] = [self.colors["fg_primary"], self.colors["fg_primary"]]
        
        # Configure scrollbars
        ctk.ThemeManager.theme["CTkScrollbar"]["fg_color"] = [self.colors["bg_secondary"], self.colors["bg_secondary"]]
        ctk.ThemeManager.theme["CTkScrollbar"]["button_color"] = [self.colors["bg_tertiary"], self.colors["bg_tertiary"]]
        ctk.ThemeManager.theme["CTkScrollbar"]["button_hover_color"] = [self.colors["hover"], self.colors["hover"]]
    
    def apply_dark_theme(self):
        """Apply the dark theme to the application."""
        self.current_theme = "dark"
        
        # Set root window colors
        self.root.configure(fg_color=self.colors["bg_primary"])
        
        # Update CustomTkinter appearance
        ctk.set_appearance_mode("dark")
        
        # Apply to all existing widgets
        self._apply_theme_to_widgets(self.root)
    
    def _apply_theme_to_widgets(self, parent):
        """Recursively apply theme to all widgets."""
        for child in parent.winfo_children():
            widget_class = child.winfo_class()
            
            # Apply theme based on widget type
            if isinstance(child, ctk.CTkFrame):
                child.configure(fg_color=self.colors["bg_secondary"])
            elif isinstance(child, ctk.CTkButton):
                child.configure(
                    fg_color=self.colors["accent_primary"],
                    hover_color=self.colors["hover"],
                    text_color=self.colors["fg_primary"]
                )
            elif isinstance(child, ctk.CTkLabel):
                child.configure(text_color=self.colors["fg_primary"])
            elif isinstance(child, ctk.CTkEntry):
                child.configure(
                    fg_color=self.colors["bg_tertiary"],
                    border_color=self.colors["border"],
                    text_color=self.colors["fg_primary"]
                )
            
            # Recursively apply to children
            self._apply_theme_to_widgets(child)
    
    def get_color(self, color_name: str) -> str:
        """
        Get a color from the current theme.
        
        Args:
            color_name: Name of the color
            
        Returns:
            Color hex code
        """
        return self.colors.get(color_name, "#ffffff")
    
    def get_button_style(self, style_type: str = "primary") -> Dict[str, Any]:
        """
        Get button style configuration.

        Args:
            style_type: Type of button style (primary, secondary, danger)

        Returns:
            Style configuration dictionary
        """
        base_style = {
            "corner_radius": 3,
            "border_width": 0,
            "text_color": self.colors["fg_primary"],
            "font": ("Segoe UI", 9),
        }
        
        if style_type == "primary":
            base_style.update({
                "fg_color": self.colors["accent_primary"],
                "hover_color": self.colors["selected"],
            })
        elif style_type == "secondary":
            base_style.update({
                "fg_color": self.colors["bg_tertiary"],
                "hover_color": self.colors["hover"],
            })
        elif style_type == "danger":
            base_style.update({
                "fg_color": self.colors["error"],
                "hover_color": "#d32f2f",
            })
        elif style_type == "accent":
            base_style.update({
                "fg_color": self.colors["accent_secondary"],
                "hover_color": "#e55a2b",
            })
        
        return base_style
    
    def get_frame_style(self, style_type: str = "primary") -> Dict[str, Any]:
        """
        Get frame style configuration.
        
        Args:
            style_type: Type of frame style (primary, secondary, tertiary)
            
        Returns:
            Style configuration dictionary
        """
        base_style = {
            "corner_radius": 8,
            "border_width": 1,
            "border_color": self.colors["border"],
        }
        
        if style_type == "primary":
            base_style.update({
                "fg_color": self.colors["bg_primary"],
            })
        elif style_type == "secondary":
            base_style.update({
                "fg_color": self.colors["bg_secondary"],
            })
        elif style_type == "tertiary":
            base_style.update({
                "fg_color": self.colors["bg_tertiary"],
            })
        elif style_type == "transparent":
            base_style.update({
                "fg_color": "transparent",
                "border_width": 0,
            })
        
        return base_style
    
    def get_entry_style(self) -> Dict[str, Any]:
        """Get entry widget style configuration."""
        return {
            "fg_color": self.colors["bg_tertiary"],
            "border_color": self.colors["border"],
            "text_color": self.colors["fg_primary"],
            "placeholder_text_color": self.colors["fg_tertiary"],
            "corner_radius": 6,
            "border_width": 1,
            "font": ("Segoe UI", 11),
        }
    
    def get_label_style(self, style_type: str = "primary") -> Dict[str, Any]:
        """
        Get label style configuration.
        
        Args:
            style_type: Type of label style (primary, secondary, tertiary)
            
        Returns:
            Style configuration dictionary
        """
        base_style = {
            "font": ("Segoe UI", 11),
        }
        
        if style_type == "primary":
            base_style.update({
                "text_color": self.colors["fg_primary"],
            })
        elif style_type == "secondary":
            base_style.update({
                "text_color": self.colors["fg_secondary"],
            })
        elif style_type == "tertiary":
            base_style.update({
                "text_color": self.colors["fg_tertiary"],
            })
        elif style_type == "heading":
            base_style.update({
                "text_color": self.colors["fg_primary"],
                "font": ("Segoe UI", 14, "bold"),
            })
        elif style_type == "title":
            base_style.update({
                "text_color": self.colors["fg_primary"],
                "font": ("Segoe UI", 16, "bold"),
            })
        
        return base_style
    
    def get_scrollbar_style(self) -> Dict[str, Any]:
        """Get scrollbar style configuration."""
        return {
            "fg_color": self.colors["bg_secondary"],
            "button_color": self.colors["bg_tertiary"],
            "button_hover_color": self.colors["hover"],
            "corner_radius": 6,
        }
    
    def create_separator(self, parent, orientation: str = "horizontal") -> ctk.CTkFrame:
        """
        Create a themed separator.
        
        Args:
            parent: Parent widget
            orientation: Separator orientation (horizontal or vertical)
            
        Returns:
            Separator frame
        """
        if orientation == "horizontal":
            separator = ctk.CTkFrame(parent, height=1, fg_color=self.colors["border"])
        else:
            separator = ctk.CTkFrame(parent, width=1, fg_color=self.colors["border"])
        
        return separator
    
    def apply_hover_effect(self, widget, enter_color: str = None, leave_color: str = None):
        """
        Apply hover effect to a widget.
        
        Args:
            widget: Widget to apply hover effect to
            enter_color: Color when mouse enters
            leave_color: Color when mouse leaves
        """
        if enter_color is None:
            enter_color = self.colors["hover"]
        if leave_color is None:
            leave_color = self.colors["bg_secondary"]
        
        def on_enter(event):
            widget.configure(fg_color=enter_color)
        
        def on_leave(event):
            widget.configure(fg_color=leave_color)
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
