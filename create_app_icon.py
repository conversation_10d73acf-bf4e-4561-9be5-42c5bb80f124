#!/usr/bin/env python3
"""
Create application icon for ArchPDF.
"""

from PIL import Image, ImageDraw
from pathlib import Path

def create_app_icon():
    """Create the main application icon."""
    # Create icon sizes
    sizes = [16, 32, 48, 64, 128, 256]
    
    # Colors
    bg_color = "#1e1e1e"
    primary_color = "#007acc"
    secondary_color = "#ff6b35"
    
    # Create icons directory
    icons_dir = Path("assets/icons")
    icons_dir.mkdir(parents=True, exist_ok=True)
    
    # Create icon for each size
    images = []
    
    for size in sizes:
        # Create image
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Calculate dimensions
        margin = size // 8
        doc_width = size - 2 * margin
        doc_height = int(doc_width * 1.3)
        
        # Center the document
        doc_x = margin
        doc_y = (size - doc_height) // 2
        
        # Draw document background
        draw.rectangle(
            [doc_x, doc_y, doc_x + doc_width, doc_y + doc_height],
            fill=primary_color,
            outline=secondary_color,
            width=max(1, size // 64)
        )
        
        # Draw document lines (text simulation)
        line_margin = size // 16
        line_height = max(1, size // 32)
        line_spacing = max(2, size // 16)
        
        for i in range(3):
            y = doc_y + line_margin + i * (line_height + line_spacing)
            if y + line_height < doc_y + doc_height - line_margin:
                line_width = doc_width - 2 * line_margin
                if i == 2:  # Make last line shorter
                    line_width = int(line_width * 0.7)
                
                draw.rectangle(
                    [doc_x + line_margin, y, doc_x + line_margin + line_width, y + line_height],
                    fill=bg_color
                )
        
        # Draw PDF text if size is large enough
        if size >= 64:
            # This would require a font, so we'll skip for now
            pass
        
        images.append(img)
    
    # Save as ICO file
    ico_path = icons_dir / "app_icon.ico"
    images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in images])
    
    # Save largest as PNG
    png_path = icons_dir / "app_icon.png"
    images[-1].save(png_path, format='PNG')
    
    print(f"Created application icon: {ico_path}")
    print(f"Created PNG icon: {png_path}")

if __name__ == "__main__":
    create_app_icon()
