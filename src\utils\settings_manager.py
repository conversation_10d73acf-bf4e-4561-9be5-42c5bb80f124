"""
Settings management for ArchPDF - Handle application settings persistence.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any
from app.config import CONFIG_DIR, DEFAULT_SETTINGS

logger = logging.getLogger(__name__)

class SettingsManager:
    """Manages application settings persistence."""
    
    def __init__(self):
        """Initialize settings manager."""
        self.settings_file = CONFIG_DIR / "settings.json"
        self.settings = DEFAULT_SETTINGS.copy()
        
        # Ensure config directory exists
        CONFIG_DIR.mkdir(exist_ok=True)
    
    def load_settings(self) -> Dict[str, Any]:
        """
        Load settings from file.
        
        Returns:
            Dictionary of settings
        """
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                
                # Merge with defaults (in case new settings were added)
                self.settings.update(saved_settings)
                logger.info("Settings loaded successfully")
            else:
                logger.info("No settings file found, using defaults")
                
        except Exception as e:
            logger.error(f"Failed to load settings: {e}")
            # Use defaults if loading fails
            self.settings = DEFAULT_SETTINGS.copy()
        
        return self.settings.copy()
    
    def save_settings(self, settings: Dict[str, Any]) -> bool:
        """
        Save settings to file.
        
        Args:
            settings: Settings dictionary to save
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.settings.update(settings)
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            
            logger.info("Settings saved successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save settings: {e}")
            return False
    
    def get_setting(self, key: str, default=None):
        """
        Get a specific setting value.
        
        Args:
            key: Setting key
            default: Default value if key not found
            
        Returns:
            Setting value or default
        """
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """
        Set a specific setting value.
        
        Args:
            key: Setting key
            value: Setting value
        """
        self.settings[key] = value
    
    def reset_to_defaults(self):
        """Reset all settings to defaults."""
        self.settings = DEFAULT_SETTINGS.copy()
        logger.info("Settings reset to defaults")
    
    def export_settings(self, file_path: str) -> bool:
        """
        Export settings to a file.
        
        Args:
            file_path: Path to export file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Settings exported to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export settings: {e}")
            return False
    
    def import_settings(self, file_path: str) -> bool:
        """
        Import settings from a file.
        
        Args:
            file_path: Path to import file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # Validate imported settings
            if isinstance(imported_settings, dict):
                self.settings.update(imported_settings)
                logger.info(f"Settings imported from {file_path}")
                return True
            else:
                logger.error("Invalid settings file format")
                return False
                
        except Exception as e:
            logger.error(f"Failed to import settings: {e}")
            return False
