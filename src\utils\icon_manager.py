"""
Icon management for ArchPDF - Handle bicolor icon set and icon operations.
"""

import tkinter as tk
from PIL import Image, ImageDraw, ImageTk
from pathlib import Path
import logging
from typing import Dict, Tuple, Optional
from app.config import ICONS_DIR, DARK_THEME

logger = logging.getLogger(__name__)

class IconManager:
    """Manages application icons and creates bicolor icon set."""
    
    def __init__(self, theme_manager):
        """
        Initialize icon manager.
        
        Args:
            theme_manager: Theme manager instance
        """
        self.theme_manager = theme_manager
        self.icon_cache: Dict[str, ImageTk.PhotoImage] = {}
        self.icon_size = (24, 24)
        
        # Ensure icons directory exists
        ICONS_DIR.mkdir(parents=True, exist_ok=True)
        
        # Create icons if they don't exist
        self._create_default_icons()
    
    def _create_default_icons(self):
        """Create default bicolor icons."""
        icons_to_create = [
            "open", "close", "save", "search", "zoom_in", "zoom_out",
            "zoom_fit", "previous", "next", "bookmark", "sidebar",
            "fullscreen", "settings", "help", "about"
        ]
        
        for icon_name in icons_to_create:
            icon_path = ICONS_DIR / f"{icon_name}.png"
            if not icon_path.exists():
                self._create_icon(icon_name, icon_path)
    
    def _create_icon(self, icon_name: str, icon_path: Path):
        """
        Create a bicolor icon.
        
        Args:
            icon_name: Name of the icon
            icon_path: Path to save the icon
        """
        try:
            # Create image
            img = Image.new('RGBA', self.icon_size, (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Get colors
            primary_color = self.theme_manager.get_color("accent_primary")
            secondary_color = self.theme_manager.get_color("accent_secondary")
            
            # Convert hex to RGB
            primary_rgb = self._hex_to_rgb(primary_color)
            secondary_rgb = self._hex_to_rgb(secondary_color)
            
            # Draw icon based on name
            if icon_name == "open":
                self._draw_open_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "close":
                self._draw_close_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "save":
                self._draw_save_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "search":
                self._draw_search_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "zoom_in":
                self._draw_zoom_in_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "zoom_out":
                self._draw_zoom_out_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "zoom_fit":
                self._draw_zoom_fit_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "previous":
                self._draw_previous_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "next":
                self._draw_next_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "bookmark":
                self._draw_bookmark_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "sidebar":
                self._draw_sidebar_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "fullscreen":
                self._draw_fullscreen_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "settings":
                self._draw_settings_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "help":
                self._draw_help_icon(draw, primary_rgb, secondary_rgb)
            elif icon_name == "about":
                self._draw_about_icon(draw, primary_rgb, secondary_rgb)
            else:
                # Default icon
                self._draw_default_icon(draw, primary_rgb, secondary_rgb)
            
            # Save icon
            img.save(icon_path, "PNG")
            logger.debug(f"Created icon: {icon_name}")
            
        except Exception as e:
            logger.error(f"Failed to create icon {icon_name}: {e}")
    
    def _hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """Convert hex color to RGB tuple."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def _draw_open_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw open folder icon."""
        # Folder base
        draw.rectangle([3, 8, 21, 20], fill=primary_rgb, outline=secondary_rgb, width=1)
        # Folder tab
        draw.rectangle([3, 6, 12, 8], fill=secondary_rgb)
    
    def _draw_close_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw close/X icon."""
        # X shape
        draw.line([6, 6, 18, 18], fill=primary_rgb, width=2)
        draw.line([18, 6, 6, 18], fill=secondary_rgb, width=2)
    
    def _draw_save_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw save/disk icon."""
        # Disk outline
        draw.rectangle([4, 4, 20, 20], fill=primary_rgb, outline=secondary_rgb, width=1)
        # Disk label
        draw.rectangle([6, 6, 18, 10], fill=secondary_rgb)
    
    def _draw_search_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw search/magnifying glass icon."""
        # Magnifying glass circle
        draw.ellipse([4, 4, 16, 16], outline=primary_rgb, width=2)
        # Handle
        draw.line([14, 14, 20, 20], fill=secondary_rgb, width=2)
    
    def _draw_zoom_in_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw zoom in icon."""
        # Magnifying glass
        draw.ellipse([3, 3, 15, 15], outline=primary_rgb, width=2)
        # Plus sign
        draw.line([9, 6, 9, 12], fill=secondary_rgb, width=2)
        draw.line([6, 9, 12, 9], fill=secondary_rgb, width=2)
        # Handle
        draw.line([13, 13, 18, 18], fill=primary_rgb, width=2)
    
    def _draw_zoom_out_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw zoom out icon."""
        # Magnifying glass
        draw.ellipse([3, 3, 15, 15], outline=primary_rgb, width=2)
        # Minus sign
        draw.line([6, 9, 12, 9], fill=secondary_rgb, width=2)
        # Handle
        draw.line([13, 13, 18, 18], fill=primary_rgb, width=2)
    
    def _draw_zoom_fit_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw zoom fit icon."""
        # Rectangle
        draw.rectangle([4, 4, 20, 16], outline=primary_rgb, width=2)
        # Arrows
        draw.line([2, 10, 4, 10], fill=secondary_rgb, width=2)
        draw.line([20, 10, 22, 10], fill=secondary_rgb, width=2)
    
    def _draw_previous_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw previous/left arrow icon."""
        # Left arrow
        points = [(18, 6), (10, 12), (18, 18)]
        draw.polygon(points, fill=primary_rgb, outline=secondary_rgb)
    
    def _draw_next_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw next/right arrow icon."""
        # Right arrow
        points = [(6, 6), (14, 12), (6, 18)]
        draw.polygon(points, fill=primary_rgb, outline=secondary_rgb)
    
    def _draw_bookmark_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw bookmark icon."""
        # Bookmark shape
        points = [(8, 4), (16, 4), (16, 20), (12, 16), (8, 20)]
        draw.polygon(points, fill=primary_rgb, outline=secondary_rgb)
    
    def _draw_sidebar_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw sidebar icon."""
        # Sidebar rectangle
        draw.rectangle([4, 4, 10, 20], fill=primary_rgb)
        # Main area
        draw.rectangle([12, 4, 20, 20], outline=secondary_rgb, width=1)
    
    def _draw_fullscreen_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw fullscreen icon."""
        # Corner brackets
        draw.line([4, 4, 4, 8], fill=primary_rgb, width=2)
        draw.line([4, 4, 8, 4], fill=primary_rgb, width=2)
        draw.line([20, 4, 20, 8], fill=secondary_rgb, width=2)
        draw.line([20, 4, 16, 4], fill=secondary_rgb, width=2)
        draw.line([4, 20, 4, 16], fill=secondary_rgb, width=2)
        draw.line([4, 20, 8, 20], fill=secondary_rgb, width=2)
        draw.line([20, 20, 20, 16], fill=primary_rgb, width=2)
        draw.line([20, 20, 16, 20], fill=primary_rgb, width=2)
    
    def _draw_settings_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw settings/gear icon."""
        # Gear shape (simplified)
        draw.ellipse([6, 6, 18, 18], outline=primary_rgb, width=2)
        draw.ellipse([9, 9, 15, 15], outline=secondary_rgb, width=1)
        # Gear teeth (simplified)
        for angle in [0, 45, 90, 135, 180, 225, 270, 315]:
            x = 12 + 8 * (angle % 90 == 0)
            y = 12 + 8 * (angle % 180 == 90)
            draw.rectangle([x-1, y-1, x+1, y+1], fill=primary_rgb)
    
    def _draw_help_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw help/question mark icon."""
        # Circle
        draw.ellipse([4, 4, 20, 20], outline=primary_rgb, width=2)
        # Question mark (simplified)
        draw.arc([8, 8, 16, 14], 0, 180, fill=secondary_rgb, width=2)
        draw.rectangle([11, 16, 13, 18], fill=secondary_rgb)
    
    def _draw_about_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw about/info icon."""
        # Circle
        draw.ellipse([4, 4, 20, 20], outline=primary_rgb, width=2)
        # i
        draw.rectangle([11, 8, 13, 10], fill=secondary_rgb)
        draw.rectangle([11, 12, 13, 18], fill=secondary_rgb)
    
    def _draw_default_icon(self, draw, primary_rgb, secondary_rgb):
        """Draw default icon."""
        # Simple rectangle
        draw.rectangle([6, 6, 18, 18], fill=primary_rgb, outline=secondary_rgb, width=1)
    
    def get_icon(self, icon_name: str, size: Optional[Tuple[int, int]] = None) -> Optional[ImageTk.PhotoImage]:
        """
        Get an icon by name.
        
        Args:
            icon_name: Name of the icon
            size: Optional size tuple (width, height)
            
        Returns:
            PhotoImage or None if not found
        """
        if size is None:
            size = self.icon_size
        
        cache_key = f"{icon_name}_{size[0]}x{size[1]}"
        
        if cache_key in self.icon_cache:
            return self.icon_cache[cache_key]
        
        try:
            icon_path = ICONS_DIR / f"{icon_name}.png"
            
            if icon_path.exists():
                img = Image.open(icon_path)
                if img.size != size:
                    img = img.resize(size, Image.Resampling.LANCZOS)
                
                photo = ImageTk.PhotoImage(img)
                self.icon_cache[cache_key] = photo
                return photo
            else:
                logger.warning(f"Icon not found: {icon_name}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to load icon {icon_name}: {e}")
            return None
    
    def clear_cache(self):
        """Clear icon cache."""
        self.icon_cache.clear()
        logger.debug("Icon cache cleared")
