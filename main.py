#!/usr/bin/env python3
"""
ArchPDF - Professional PDF Reader Application
A modern, standalone PDF reader for Windows with dark theme and bicolor icons.

Author: Augment Agent
Version: 1.0.0
"""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """Main entry point for the ArchPDF application."""
    try:
        from app.main_window import ArchPDFApp
        app = ArchPDFApp()
        app.run()
    except ImportError as e:
        print(f"Error importing application modules: {e}")
        print("Please ensure all dependencies are installed.")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
