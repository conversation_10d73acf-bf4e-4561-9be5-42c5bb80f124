"""
Application settings management for ArchPDF - User preferences and configuration.
"""

import tkinter as tk
import customtkinter as ctk
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class SettingsDialog(ctk.CTkToplevel):
    """Settings dialog for user preferences."""
    
    def __init__(self, parent, app):
        """
        Initialize the settings dialog.
        
        Args:
            parent: Parent window
            app: Main application instance
        """
        super().__init__(parent)
        self.app = app
        self.settings = app.settings.copy()
        
        # Configure window
        self.title("Settings")
        self.geometry("500x400")
        self.resizable(False, False)
        
        # Make dialog modal
        self.transient(parent)
        self.grab_set()
        
        # Center on parent
        self._center_on_parent(parent)
        
        # Create UI
        self._create_ui()
        
        # Load current settings
        self._load_settings()
    
    def _center_on_parent(self, parent):
        """Center dialog on parent window."""
        parent.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 250
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 200
        self.geometry(f"500x400+{x}+{y}")
    
    def _create_ui(self):
        """Create the settings dialog UI."""
        # Main frame
        main_frame = ctk.CTkFrame(self, **self.app.theme_manager.get_frame_style("primary"))
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Tabview for different setting categories
        self.tabview = ctk.CTkTabview(main_frame)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=(10, 5))
        
        # Create tabs
        self._create_general_tab()
        self._create_display_tab()
        self._create_advanced_tab()
        
        # Button frame
        button_frame = ctk.CTkFrame(main_frame, **self.app.theme_manager.get_frame_style("transparent"))
        button_frame.pack(fill="x", padx=10, pady=(5, 10))
        
        # Buttons
        self.ok_btn = ctk.CTkButton(
            button_frame,
            text="OK",
            command=self._save_and_close,
            **self.app.theme_manager.get_button_style("primary")
        )
        self.ok_btn.pack(side="left", padx=(0, 5))
        
        self.cancel_btn = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=self.destroy,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.cancel_btn.pack(side="left", padx=5)
        
        self.apply_btn = ctk.CTkButton(
            button_frame,
            text="Apply",
            command=self._apply_settings,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.apply_btn.pack(side="left", padx=5)
        
        self.reset_btn = ctk.CTkButton(
            button_frame,
            text="Reset to Defaults",
            command=self._reset_to_defaults,
            **self.app.theme_manager.get_button_style("danger")
        )
        self.reset_btn.pack(side="right")
    
    def _create_general_tab(self):
        """Create general settings tab."""
        self.general_tab = self.tabview.add("General")
        
        # Recent files limit
        recent_frame = ctk.CTkFrame(self.general_tab, **self.app.theme_manager.get_frame_style("secondary"))
        recent_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        recent_label = ctk.CTkLabel(
            recent_frame,
            text="Recent Files Limit:",
            **self.app.theme_manager.get_label_style("primary")
        )
        recent_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.recent_files_var = tk.StringVar()
        self.recent_files_entry = ctk.CTkEntry(
            recent_frame,
            textvariable=self.recent_files_var,
            width=100,
            **self.app.theme_manager.get_entry_style()
        )
        self.recent_files_entry.pack(anchor="w", padx=10, pady=(0, 10))
        
        # Auto-save settings
        autosave_frame = ctk.CTkFrame(self.general_tab, **self.app.theme_manager.get_frame_style("secondary"))
        autosave_frame.pack(fill="x", padx=10, pady=5)
        
        self.auto_save_var = tk.BooleanVar()
        self.auto_save_cb = ctk.CTkCheckBox(
            autosave_frame,
            text="Auto-save settings on exit",
            variable=self.auto_save_var,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.auto_save_cb.pack(anchor="w", padx=10, pady=10)
    
    def _create_display_tab(self):
        """Create display settings tab."""
        self.display_tab = self.tabview.add("Display")
        
        # Default zoom
        zoom_frame = ctk.CTkFrame(self.display_tab, **self.app.theme_manager.get_frame_style("secondary"))
        zoom_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        zoom_label = ctk.CTkLabel(
            zoom_frame,
            text="Default Zoom Level (%):",
            **self.app.theme_manager.get_label_style("primary")
        )
        zoom_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.zoom_var = tk.StringVar()
        self.zoom_entry = ctk.CTkEntry(
            zoom_frame,
            textvariable=self.zoom_var,
            width=100,
            **self.app.theme_manager.get_entry_style()
        )
        self.zoom_entry.pack(anchor="w", padx=10, pady=(0, 10))
        
        # Fit mode
        fit_frame = ctk.CTkFrame(self.display_tab, **self.app.theme_manager.get_frame_style("secondary"))
        fit_frame.pack(fill="x", padx=10, pady=5)
        
        fit_label = ctk.CTkLabel(
            fit_frame,
            text="Default Fit Mode:",
            **self.app.theme_manager.get_label_style("primary")
        )
        fit_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.fit_mode_var = tk.StringVar()
        self.fit_mode_combo = ctk.CTkComboBox(
            fit_frame,
            variable=self.fit_mode_var,
            values=["None", "Fit Width", "Fit Page"],
            state="readonly",
            **self.app.theme_manager.get_entry_style()
        )
        self.fit_mode_combo.pack(anchor="w", padx=10, pady=(0, 10))
        
        # UI visibility
        ui_frame = ctk.CTkFrame(self.display_tab, **self.app.theme_manager.get_frame_style("secondary"))
        ui_frame.pack(fill="x", padx=10, pady=5)
        
        ui_label = ctk.CTkLabel(
            ui_frame,
            text="UI Elements:",
            **self.app.theme_manager.get_label_style("primary")
        )
        ui_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.show_sidebar_var = tk.BooleanVar()
        self.show_sidebar_cb = ctk.CTkCheckBox(
            ui_frame,
            text="Show sidebar",
            variable=self.show_sidebar_var,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.show_sidebar_cb.pack(anchor="w", padx=20, pady=2)
        
        self.show_toolbar_var = tk.BooleanVar()
        self.show_toolbar_cb = ctk.CTkCheckBox(
            ui_frame,
            text="Show toolbar",
            variable=self.show_toolbar_var,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.show_toolbar_cb.pack(anchor="w", padx=20, pady=2)
        
        self.show_statusbar_var = tk.BooleanVar()
        self.show_statusbar_cb = ctk.CTkCheckBox(
            ui_frame,
            text="Show status bar",
            variable=self.show_statusbar_var,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.show_statusbar_cb.pack(anchor="w", padx=20, pady=(2, 10))
    
    def _create_advanced_tab(self):
        """Create advanced settings tab."""
        self.advanced_tab = self.tabview.add("Advanced")
        
        # Window settings
        window_frame = ctk.CTkFrame(self.advanced_tab, **self.app.theme_manager.get_frame_style("secondary"))
        window_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        window_label = ctk.CTkLabel(
            window_frame,
            text="Window Settings:",
            **self.app.theme_manager.get_label_style("primary")
        )
        window_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        # Window size
        size_frame = ctk.CTkFrame(window_frame, **self.app.theme_manager.get_frame_style("transparent"))
        size_frame.pack(fill="x", padx=10, pady=5)
        
        width_label = ctk.CTkLabel(
            size_frame,
            text="Width:",
            **self.app.theme_manager.get_label_style("secondary")
        )
        width_label.pack(side="left", padx=(0, 5))
        
        self.window_width_var = tk.StringVar()
        self.window_width_entry = ctk.CTkEntry(
            size_frame,
            textvariable=self.window_width_var,
            width=80,
            **self.app.theme_manager.get_entry_style()
        )
        self.window_width_entry.pack(side="left", padx=5)
        
        height_label = ctk.CTkLabel(
            size_frame,
            text="Height:",
            **self.app.theme_manager.get_label_style("secondary")
        )
        height_label.pack(side="left", padx=(20, 5))
        
        self.window_height_var = tk.StringVar()
        self.window_height_entry = ctk.CTkEntry(
            size_frame,
            textvariable=self.window_height_var,
            width=80,
            **self.app.theme_manager.get_entry_style()
        )
        self.window_height_entry.pack(side="left", padx=5)
        
        # Sidebar width
        sidebar_label = ctk.CTkLabel(
            window_frame,
            text="Sidebar Width:",
            **self.app.theme_manager.get_label_style("secondary")
        )
        sidebar_label.pack(anchor="w", padx=10, pady=(5, 2))
        
        self.sidebar_width_var = tk.StringVar()
        self.sidebar_width_entry = ctk.CTkEntry(
            window_frame,
            textvariable=self.sidebar_width_var,
            width=100,
            **self.app.theme_manager.get_entry_style()
        )
        self.sidebar_width_entry.pack(anchor="w", padx=10, pady=(0, 10))
    
    def _load_settings(self):
        """Load current settings into the dialog."""
        # General settings
        self.recent_files_var.set(str(self.settings.get('recent_files_limit', 10)))
        self.auto_save_var.set(self.settings.get('auto_save_settings', True))
        
        # Display settings
        self.zoom_var.set(str(self.settings.get('zoom_level', 100)))
        
        fit_mode = self.settings.get('fit_mode', 'fit_width')
        fit_mode_display = {
            'none': 'None',
            'fit_width': 'Fit Width',
            'fit_page': 'Fit Page'
        }.get(fit_mode, 'Fit Width')
        self.fit_mode_var.set(fit_mode_display)
        
        self.show_sidebar_var.set(self.settings.get('show_sidebar', True))
        self.show_toolbar_var.set(self.settings.get('show_toolbar', True))
        self.show_statusbar_var.set(self.settings.get('show_statusbar', True))
        
        # Advanced settings
        self.window_width_var.set(str(self.settings.get('window_width', 1200)))
        self.window_height_var.set(str(self.settings.get('window_height', 800)))
        self.sidebar_width_var.set(str(self.settings.get('sidebar_width', 250)))
    
    def _apply_settings(self):
        """Apply settings without closing dialog."""
        try:
            # General settings
            self.settings['recent_files_limit'] = int(self.recent_files_var.get())
            self.settings['auto_save_settings'] = self.auto_save_var.get()
            
            # Display settings
            self.settings['zoom_level'] = int(self.zoom_var.get())
            
            fit_mode_map = {
                'None': 'none',
                'Fit Width': 'fit_width',
                'Fit Page': 'fit_page'
            }
            self.settings['fit_mode'] = fit_mode_map.get(self.fit_mode_var.get(), 'fit_width')
            
            self.settings['show_sidebar'] = self.show_sidebar_var.get()
            self.settings['show_toolbar'] = self.show_toolbar_var.get()
            self.settings['show_statusbar'] = self.show_statusbar_var.get()
            
            # Advanced settings
            self.settings['window_width'] = int(self.window_width_var.get())
            self.settings['window_height'] = int(self.window_height_var.get())
            self.settings['sidebar_width'] = int(self.sidebar_width_var.get())
            
            # Apply to application
            self.app.settings.update(self.settings)
            self.app.settings_manager.save_settings(self.settings)
            
            # Apply UI changes
            self._apply_ui_changes()
            
            logger.info("Settings applied successfully")
            
        except ValueError as e:
            tk.messagebox.showerror("Invalid Input", f"Please enter valid numeric values: {e}")
        except Exception as e:
            logger.error(f"Failed to apply settings: {e}")
            tk.messagebox.showerror("Error", f"Failed to apply settings: {e}")
    
    def _apply_ui_changes(self):
        """Apply UI changes immediately."""
        # Update toolbar visibility
        if self.settings['show_toolbar']:
            self.app.toolbar.pack(fill="x", padx=5, pady=(5, 0), before=self.app.content_frame)
        else:
            self.app.toolbar.pack_forget()
        
        # Update status bar visibility
        if self.settings['show_statusbar']:
            self.app.statusbar.pack(fill="x", side="bottom", padx=5, pady=(0, 5))
        else:
            self.app.statusbar.pack_forget()
        
        # Update sidebar visibility
        if self.settings['show_sidebar']:
            self.app.sidebar.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        else:
            self.app.sidebar.grid_remove()
    
    def _save_and_close(self):
        """Save settings and close dialog."""
        self._apply_settings()
        self.destroy()
    
    def _reset_to_defaults(self):
        """Reset all settings to defaults."""
        from app.config import DEFAULT_SETTINGS
        self.settings = DEFAULT_SETTINGS.copy()
        self._load_settings()
        logger.info("Settings reset to defaults")
