"""
Sidebar component for ArchPDF - Shows bookmarks and thumbnails.
"""

import tkinter as tk
import customtkinter as ctk
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class SideBar(ctk.CTkFrame):
    """Sidebar with bookmarks and thumbnails."""
    
    def __init__(self, parent, app):
        """
        Initialize the sidebar.
        
        Args:
            parent: Parent widget
            app: Main application instance
        """
        super().__init__(parent, **app.theme_manager.get_frame_style("secondary"))
        self.app = app
        
        # Configure size
        self.configure(width=250)
        
        # Create sidebar content
        self._create_content()
        
        # Current tab
        self.current_tab = "bookmarks"
    
    def _create_content(self):
        """Create sidebar content."""
        # Configure grid
        self.grid_rowconfigure(1, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
        # Tab buttons
        self.tab_frame = ctk.CTkFrame(self, **self.app.theme_manager.get_frame_style("tertiary"))
        self.tab_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=(5, 0))
        self.tab_frame.grid_columnconfigure(0, weight=1)
        self.tab_frame.grid_columnconfigure(1, weight=1)
        
        self.bookmarks_tab = ctk.CTkButton(
            self.tab_frame,
            text="Bookmarks",
            command=lambda: self._switch_tab("bookmarks"),
            **self.app.theme_manager.get_button_style("primary")
        )
        self.bookmarks_tab.grid(row=0, column=0, padx=(5, 2), pady=5, sticky="ew")
        
        self.thumbnails_tab = ctk.CTkButton(
            self.tab_frame,
            text="Thumbnails",
            command=lambda: self._switch_tab("thumbnails"),
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.thumbnails_tab.grid(row=0, column=1, padx=(2, 5), pady=5, sticky="ew")
        
        # Content area
        self.content_frame = ctk.CTkFrame(self, **self.app.theme_manager.get_frame_style("primary"))
        self.content_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)
        
        # Bookmarks view
        self._create_bookmarks_view()
        
        # Thumbnails view
        self._create_thumbnails_view()
        
        # Show default view
        self._switch_tab("bookmarks")
    
    def _create_bookmarks_view(self):
        """Create bookmarks view."""
        self.bookmarks_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            **self.app.theme_manager.get_frame_style("transparent")
        )
        
        # Placeholder label
        self.bookmarks_placeholder = ctk.CTkLabel(
            self.bookmarks_frame,
            text="No bookmarks available",
            **self.app.theme_manager.get_label_style("tertiary")
        )
        self.bookmarks_placeholder.pack(pady=20)
    
    def _create_thumbnails_view(self):
        """Create thumbnails view."""
        self.thumbnails_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            **self.app.theme_manager.get_frame_style("transparent")
        )
        
        # Placeholder label
        self.thumbnails_placeholder = ctk.CTkLabel(
            self.thumbnails_frame,
            text="No thumbnails available",
            **self.app.theme_manager.get_label_style("tertiary")
        )
        self.thumbnails_placeholder.pack(pady=20)
    
    def _switch_tab(self, tab_name: str):
        """
        Switch to a different tab.
        
        Args:
            tab_name: Name of the tab to switch to
        """
        self.current_tab = tab_name
        
        # Hide all views
        self.bookmarks_frame.grid_remove()
        self.thumbnails_frame.grid_remove()
        
        # Update button styles
        if tab_name == "bookmarks":
            self.bookmarks_tab.configure(**self.app.theme_manager.get_button_style("primary"))
            self.thumbnails_tab.configure(**self.app.theme_manager.get_button_style("secondary"))
            self.bookmarks_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        else:
            self.bookmarks_tab.configure(**self.app.theme_manager.get_button_style("secondary"))
            self.thumbnails_tab.configure(**self.app.theme_manager.get_button_style("primary"))
            self.thumbnails_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
    
    def load_document(self):
        """Load document content into sidebar."""
        if not self.app.pdf_engine.is_document_loaded():
            self.clear()
            return
        
        # Load bookmarks
        self._load_bookmarks()
        
        # Load thumbnails (placeholder for now)
        self._load_thumbnails()
    
    def _load_bookmarks(self):
        """Load document bookmarks."""
        # Clear existing bookmarks
        for widget in self.bookmarks_frame.winfo_children():
            if widget != self.bookmarks_placeholder:
                widget.destroy()
        
        # Get bookmarks from PDF
        bookmarks = self.app.pdf_engine.get_bookmarks()
        
        if bookmarks:
            self.bookmarks_placeholder.pack_forget()
            
            for bookmark in bookmarks:
                self._create_bookmark_item(bookmark)
        else:
            self.bookmarks_placeholder.pack(pady=20)
    
    def _create_bookmark_item(self, bookmark: Dict[str, Any]):
        """
        Create a bookmark item.
        
        Args:
            bookmark: Bookmark information
        """
        level = bookmark.get('level', 1)
        title = bookmark.get('title', 'Untitled')
        page = bookmark.get('page', 0)
        
        # Create bookmark button with indentation based on level
        indent = "  " * (level - 1)
        text = f"{indent}{title}"
        
        bookmark_btn = ctk.CTkButton(
            self.bookmarks_frame,
            text=text,
            anchor="w",
            command=lambda p=page: self.app.go_to_page(p + 1),  # Convert to 1-based
            **self.app.theme_manager.get_button_style("secondary")
        )
        bookmark_btn.pack(fill="x", padx=5, pady=1)
        
        # Apply hover effect
        self.app.theme_manager.apply_hover_effect(bookmark_btn)
    
    def _load_thumbnails(self):
        """Load page thumbnails (placeholder implementation)."""
        # Clear existing thumbnails
        for widget in self.thumbnails_frame.winfo_children():
            if widget != self.thumbnails_placeholder:
                widget.destroy()
        
        # For now, just show placeholder
        # In a full implementation, this would generate thumbnail images
        if self.app.pdf_engine.is_document_loaded():
            self.thumbnails_placeholder.configure(text="Thumbnails coming soon...")
        else:
            self.thumbnails_placeholder.configure(text="No thumbnails available")
        
        self.thumbnails_placeholder.pack(pady=20)
    
    def clear(self):
        """Clear sidebar content."""
        # Clear bookmarks
        for widget in self.bookmarks_frame.winfo_children():
            if widget != self.bookmarks_placeholder:
                widget.destroy()
        
        # Clear thumbnails
        for widget in self.thumbnails_frame.winfo_children():
            if widget != self.thumbnails_placeholder:
                widget.destroy()
        
        # Show placeholders
        self.bookmarks_placeholder.pack(pady=20)
        self.thumbnails_placeholder.pack(pady=20)
        
        # Reset placeholder text
        self.bookmarks_placeholder.configure(text="No bookmarks available")
        self.thumbnails_placeholder.configure(text="No thumbnails available")
