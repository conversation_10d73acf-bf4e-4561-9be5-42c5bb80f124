"""
Configuration settings for ArchPDF application.
"""

import os
from pathlib import Path

# Application Information
APP_NAME = "ArchPDF"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Augment Agent"
APP_DESCRIPTION = "Professional PDF Reader for Windows"

# Paths
APP_DIR = Path(__file__).parent.parent.parent
ASSETS_DIR = APP_DIR / "assets"
ICONS_DIR = ASSETS_DIR / "icons"
THEMES_DIR = ASSETS_DIR / "themes"
CONFIG_DIR = Path.home() / ".archpdf"

# Ensure directories exist
CONFIG_DIR.mkdir(exist_ok=True)

# Default Settings
DEFAULT_SETTINGS = {
    "theme": "dark",
    "zoom_level": 100,
    "fit_mode": "fit_width",
    "recent_files_limit": 10,
    "window_width": 1200,
    "window_height": 800,
    "sidebar_width": 250,
    "show_sidebar": True,
    "show_toolbar": True,
    "show_statusbar": True,
    "auto_save_settings": True,
}

# Color Palette for Dark Theme
DARK_THEME = {
    "bg_primary": "#1e1e1e",      # Main background
    "bg_secondary": "#2d2d2d",    # Secondary background
    "bg_tertiary": "#3e3e3e",     # Tertiary background
    "fg_primary": "#ffffff",      # Primary text
    "fg_secondary": "#cccccc",    # Secondary text
    "fg_tertiary": "#999999",     # Tertiary text
    "accent_primary": "#007acc",  # Primary accent (blue)
    "accent_secondary": "#ff6b35", # Secondary accent (orange)
    "border": "#555555",          # Border color
    "hover": "#404040",           # Hover state
    "selected": "#094771",        # Selected state
    "error": "#f44747",           # Error color
    "warning": "#ffcc02",         # Warning color
    "success": "#89d185",         # Success color
}

# File Extensions
SUPPORTED_EXTENSIONS = [".pdf"]

# Zoom Settings
MIN_ZOOM = 25
MAX_ZOOM = 500
ZOOM_STEP = 25

# Recent Files
MAX_RECENT_FILES = 10
