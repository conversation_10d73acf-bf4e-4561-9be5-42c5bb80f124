"""
PDF Engine for ArchPDF - Core PDF processing functionality using PyMuPDF.
"""

import fitz  # PyMuPDF
from typing import Optional, List, Tuple, Dict, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class PDFEngine:
    """Core PDF processing engine using PyMuPDF."""
    
    def __init__(self):
        """Initialize the PDF engine."""
        self.document: Optional[fitz.Document] = None
        self.file_path: Optional[Path] = None
        self.page_count: int = 0
        self.current_page: int = 0
        self.zoom_level: float = 1.0
        self.rotation: int = 0
        
    def load_document(self, file_path: str) -> bool:
        """
        Load a PDF document.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.file_path = Path(file_path)
            self.document = fitz.open(file_path)
            self.page_count = len(self.document)
            self.current_page = 0
            logger.info(f"Loaded PDF: {file_path} ({self.page_count} pages)")
            return True
        except Exception as e:
            logger.error(f"Failed to load PDF {file_path}: {e}")
            return False
    
    def close_document(self):
        """Close the current document."""
        if self.document:
            self.document.close()
            self.document = None
            self.file_path = None
            self.page_count = 0
            self.current_page = 0
    
    def get_page_image(self, page_num: int, zoom: float = 1.0, rotation: int = 0) -> Optional[bytes]:
        """
        Render a page as an image.
        
        Args:
            page_num: Page number (0-based)
            zoom: Zoom factor
            rotation: Rotation angle (0, 90, 180, 270)
            
        Returns:
            Image data as bytes or None if error
        """
        if not self.document or page_num < 0 or page_num >= self.page_count:
            return None
            
        try:
            page = self.document[page_num]
            # Create transformation matrix for zoom and rotation
            mat = fitz.Matrix(zoom, zoom).prerotate(rotation)
            pix = page.get_pixmap(matrix=mat)
            return pix.tobytes("png")
        except Exception as e:
            logger.error(f"Failed to render page {page_num}: {e}")
            return None
    
    def get_page_size(self, page_num: int) -> Optional[Tuple[float, float]]:
        """
        Get the size of a page.
        
        Args:
            page_num: Page number (0-based)
            
        Returns:
            (width, height) tuple or None if error
        """
        if not self.document or page_num < 0 or page_num >= self.page_count:
            return None
            
        try:
            page = self.document[page_num]
            rect = page.rect
            return (rect.width, rect.height)
        except Exception as e:
            logger.error(f"Failed to get page size for page {page_num}: {e}")
            return None
    
    def search_text(self, query: str, page_num: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Search for text in the document.
        
        Args:
            query: Text to search for
            page_num: Specific page to search (None for all pages)
            
        Returns:
            List of search results with page numbers and coordinates
        """
        if not self.document or not query:
            return []
            
        results = []
        pages_to_search = [page_num] if page_num is not None else range(self.page_count)
        
        try:
            for page_idx in pages_to_search:
                if page_idx < 0 or page_idx >= self.page_count:
                    continue
                    
                page = self.document[page_idx]
                text_instances = page.search_for(query)
                
                for rect in text_instances:
                    results.append({
                        'page': page_idx,
                        'text': query,
                        'bbox': (rect.x0, rect.y0, rect.x1, rect.y1)
                    })
                    
        except Exception as e:
            logger.error(f"Failed to search text '{query}': {e}")
            
        return results
    
    def get_page_text(self, page_num: int) -> str:
        """
        Extract text from a page.
        
        Args:
            page_num: Page number (0-based)
            
        Returns:
            Text content of the page
        """
        if not self.document or page_num < 0 or page_num >= self.page_count:
            return ""
            
        try:
            page = self.document[page_num]
            return page.get_text()
        except Exception as e:
            logger.error(f"Failed to extract text from page {page_num}: {e}")
            return ""
    
    def get_bookmarks(self) -> List[Dict[str, Any]]:
        """
        Get document bookmarks/outline.
        
        Returns:
            List of bookmarks with title, page, and level
        """
        if not self.document:
            return []
            
        try:
            toc = self.document.get_toc()
            bookmarks = []
            
            for item in toc:
                level, title, page = item
                bookmarks.append({
                    'level': level,
                    'title': title,
                    'page': page - 1  # Convert to 0-based
                })
                
            return bookmarks
        except Exception as e:
            logger.error(f"Failed to get bookmarks: {e}")
            return []
    
    def get_document_info(self) -> Dict[str, Any]:
        """
        Get document metadata.
        
        Returns:
            Dictionary with document information
        """
        if not self.document:
            return {}
            
        try:
            metadata = self.document.metadata
            return {
                'title': metadata.get('title', ''),
                'author': metadata.get('author', ''),
                'subject': metadata.get('subject', ''),
                'creator': metadata.get('creator', ''),
                'producer': metadata.get('producer', ''),
                'creation_date': metadata.get('creationDate', ''),
                'modification_date': metadata.get('modDate', ''),
                'page_count': self.page_count,
                'file_size': self.file_path.stat().st_size if self.file_path else 0
            }
        except Exception as e:
            logger.error(f"Failed to get document info: {e}")
            return {}
    
    def is_document_loaded(self) -> bool:
        """Check if a document is currently loaded."""
        return self.document is not None
    
    def get_current_page(self) -> int:
        """Get the current page number."""
        return self.current_page
    
    def set_current_page(self, page_num: int) -> bool:
        """
        Set the current page number.
        
        Args:
            page_num: Page number (0-based)
            
        Returns:
            True if successful, False otherwise
        """
        if 0 <= page_num < self.page_count:
            self.current_page = page_num
            return True
        return False
    
    def next_page(self) -> bool:
        """Go to the next page."""
        if self.current_page < self.page_count - 1:
            self.current_page += 1
            return True
        return False
    
    def previous_page(self) -> bool:
        """Go to the previous page."""
        if self.current_page > 0:
            self.current_page -= 1
            return True
        return False
