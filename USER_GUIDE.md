# ArchPDF User Guide

## Getting Started

ArchPDF is a professional PDF reader designed for Windows with a modern dark theme and intuitive interface.

### Opening Documents

**Method 1: File Menu**
1. Click **File > Open** or press `Ctrl+O`
2. Browse and select your PDF file
3. Click **Open**

**Method 2: Recent Files**
1. Click **File > Recent Files**
2. Select from recently opened documents

**Method 3: Drag and Drop** (if implemented)
- Drag a PDF file onto the application window

### Interface Overview

#### Main Window Components

**Menu Bar**
- **File**: Open, recent files, close, exit
- **View**: Zoom controls, navigation, display options
- **Tools**: Search, document properties, settings
- **Help**: Keyboard shortcuts, about

**Toolbar**
- Open file button
- Navigation controls (previous/next page)
- Page number input and display
- Zoom controls (in, out, fit width, fit page)
- Search button
- Sidebar toggle

**Main Viewing Area**
- PDF document display
- Mouse wheel scrolling
- Click and drag to pan
- Ctrl+mouse wheel to zoom

**Sidebar** (toggleable)
- **Bookmarks tab**: Document outline navigation
- **Thumbnails tab**: Page preview navigation

**Status Bar**
- Current page information
- Status messages
- Zoom level display

## Navigation

### Page Navigation

**Using Toolbar**
- Click **◀** (Previous) or **▶** (Next) buttons
- Enter page number in the page input field

**Using Keyboard**
- `Page Up` / `Page Down`: Navigate pages
- `Home` / `End`: First / Last page
- `Ctrl+G`: Go to specific page

**Using Bookmarks**
- Click on bookmarks in the sidebar to jump to sections

### Zoom Controls

**Toolbar Buttons**
- **+**: Zoom in
- **-**: Zoom out
- **Fit Width**: Fit page width to window
- **Fit Page**: Fit entire page to window

**Keyboard Shortcuts**
- `Ctrl++`: Zoom in
- `Ctrl+-`: Zoom out
- `Ctrl+0`: Actual size (100%)

**Mouse Controls**
- `Ctrl+Mouse Wheel`: Zoom in/out
- `Mouse Wheel`: Scroll vertically
- `Click and Drag`: Pan the document

## Search Functionality

### Basic Search

1. Press `Ctrl+F` or click the **Search** button
2. Enter your search term
3. Click **Find All** to search the entire document
4. Use **Previous** and **Next** to navigate results

### Advanced Search Options

**Case Sensitive**
- Check to match exact letter case

**Whole Words Only**
- Check to find complete words only

**Regular Expression**
- Check to use regex patterns for complex searches

### Search Results

- Results counter shows current result and total found
- Click **Previous** or **Next** to navigate between results
- The viewer automatically jumps to the page containing the result

## Customization

### Settings Dialog

Access via **Tools > Settings** to configure:

**General Tab**
- Recent files limit
- Auto-save settings preference

**Display Tab**
- Default zoom level
- Default fit mode
- UI element visibility (sidebar, toolbar, status bar)

**Advanced Tab**
- Window dimensions
- Sidebar width

### Keyboard Shortcuts

| Action | Shortcut |
|--------|----------|
| Open file | `Ctrl+O` |
| Find text | `Ctrl+F` |
| Quit application | `Ctrl+Q` |
| Previous page | `Page Up` |
| Next page | `Page Down` |
| Zoom in | `Ctrl++` |
| Zoom out | `Ctrl+-` |
| Actual size | `Ctrl+0` |
| Toggle fullscreen | `F11` |

## Tips and Tricks

### Performance Optimization

**For Large Documents**
- Use fit modes instead of high zoom levels
- Close other applications to free memory
- Navigate using bookmarks for quick access

**For Better Viewing**
- Use **Fit Width** for reading text
- Use **Fit Page** for viewing entire pages
- Adjust window size for optimal viewing

### Workflow Tips

**Document Review**
- Use bookmarks for quick navigation between sections
- Use search to find specific terms or references
- Toggle sidebar to maximize viewing area

**Presentation Mode**
- Press `F11` for fullscreen viewing
- Use **Fit Page** to display full pages
- Hide toolbar and status bar in settings for clean view

## Troubleshooting

### Common Issues

**Document Won't Open**
- Verify the file is a valid PDF
- Check if the file is password-protected
- Ensure sufficient disk space and memory

**Slow Performance**
- Reduce zoom level
- Close other applications
- Restart ArchPDF

**Search Not Working**
- Ensure the PDF contains searchable text
- Try different search terms
- Check if the document is image-based

**Display Issues**
- Update graphics drivers
- Try different zoom levels
- Restart the application

### Getting Support

1. Check application logs in `%USERPROFILE%\.archpdf\logs\`
2. Note the specific error message or behavior
3. Include system information when reporting issues

## Advanced Features

### Document Properties

View document metadata via **Tools > Document Properties**:
- Title, author, subject
- Creation and modification dates
- Page count and file size
- Creator and producer information

### Recent Files Management

- Automatically tracks recently opened files
- Configurable limit in settings
- Clear recent files via **File > Recent Files > Clear Recent Files**

### Error Handling

ArchPDF includes comprehensive error handling:
- User-friendly error messages
- Automatic error logging
- Recovery suggestions for common issues

## Accessibility

ArchPDF supports:
- High contrast dark theme
- Keyboard navigation
- Scalable interface elements
- Clear visual feedback
