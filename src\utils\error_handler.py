"""
Error handling utilities for ArchPDF - Comprehensive error management and user feedback.
"""

import logging
import traceback
import tkinter as tk
from tkinter import messagebox
import customtkinter as ctk
from typing import Optional, Callable, Any
from functools import wraps
import sys

logger = logging.getLogger(__name__)

class ErrorHandler:
    """Centralized error handling for the application."""
    
    def __init__(self, app=None):
        """
        Initialize error handler.
        
        Args:
            app: Main application instance
        """
        self.app = app
        self.error_count = 0
        self.last_error_time = 0
        
        # Set up logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Set up logging configuration."""
        # Create logs directory
        from app.config import CONFIG_DIR
        log_dir = CONFIG_DIR / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # Configure file handler
        log_file = log_dir / "archpdf.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Configure console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """
        Handle uncaught exceptions.
        
        Args:
            exc_type: Exception type
            exc_value: Exception value
            exc_traceback: Exception traceback
        """
        if issubclass(exc_type, KeyboardInterrupt):
            # Allow keyboard interrupt to work normally
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # Log the exception
        logger.critical(
            "Uncaught exception",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
        
        # Show error dialog
        error_msg = f"An unexpected error occurred:\n\n{exc_type.__name__}: {exc_value}"
        self.show_error_dialog("Critical Error", error_msg, show_details=True)
        
        self.error_count += 1
    
    def show_error_dialog(self, title: str, message: str, 
                         error_type: str = "error", show_details: bool = False):
        """
        Show error dialog to user.
        
        Args:
            title: Dialog title
            message: Error message
            error_type: Type of error (error, warning, info)
            show_details: Whether to show detailed error information
        """
        try:
            if error_type == "warning":
                messagebox.showwarning(title, message)
            elif error_type == "info":
                messagebox.showinfo(title, message)
            else:
                messagebox.showerror(title, message)
                
            # Update status bar if available
            if self.app and hasattr(self.app, 'statusbar'):
                self.app.statusbar.set_status(f"Error: {title}")
                
        except Exception as e:
            # Fallback error handling
            logger.error(f"Failed to show error dialog: {e}")
            print(f"ERROR: {title} - {message}")
    
    def show_progress_dialog(self, title: str, message: str, parent=None):
        """
        Show progress dialog for long operations.
        
        Args:
            title: Dialog title
            message: Progress message
            parent: Parent window
            
        Returns:
            Progress dialog instance
        """
        return ProgressDialog(title, message, parent, self.app)
    
    def handle_pdf_error(self, error: Exception, file_path: str = ""):
        """
        Handle PDF-specific errors.
        
        Args:
            error: The exception that occurred
            file_path: Path to the PDF file
        """
        error_msg = str(error)
        
        if "password" in error_msg.lower():
            self.show_error_dialog(
                "Password Protected",
                f"The PDF file is password protected.\n\nFile: {file_path}",
                "warning"
            )
        elif "corrupt" in error_msg.lower() or "damaged" in error_msg.lower():
            self.show_error_dialog(
                "Corrupted File",
                f"The PDF file appears to be corrupted or damaged.\n\nFile: {file_path}",
                "error"
            )
        elif "not found" in error_msg.lower():
            self.show_error_dialog(
                "File Not Found",
                f"The specified file could not be found.\n\nFile: {file_path}",
                "error"
            )
        elif "permission" in error_msg.lower():
            self.show_error_dialog(
                "Permission Denied",
                f"Permission denied accessing the file.\n\nFile: {file_path}",
                "error"
            )
        else:
            self.show_error_dialog(
                "PDF Error",
                f"Failed to process PDF file:\n\n{error_msg}\n\nFile: {file_path}",
                "error"
            )
        
        logger.error(f"PDF error for {file_path}: {error}")
    
    def handle_memory_error(self, error: MemoryError, operation: str = ""):
        """
        Handle memory errors.
        
        Args:
            error: Memory error
            operation: Operation that caused the error
        """
        message = f"Insufficient memory to complete the operation"
        if operation:
            message += f": {operation}"
        
        message += "\n\nTry:\n• Closing other applications\n• Reducing zoom level\n• Restarting the application"
        
        self.show_error_dialog("Memory Error", message, "error")
        logger.error(f"Memory error during {operation}: {error}")
    
    def handle_file_error(self, error: Exception, file_path: str = "", operation: str = ""):
        """
        Handle file operation errors.
        
        Args:
            error: File operation error
            file_path: Path to the file
            operation: Operation being performed
        """
        error_msg = str(error)
        
        if isinstance(error, FileNotFoundError):
            title = "File Not Found"
            message = f"The file could not be found.\n\nFile: {file_path}"
        elif isinstance(error, PermissionError):
            title = "Permission Denied"
            message = f"Permission denied accessing the file.\n\nFile: {file_path}"
        elif isinstance(error, OSError):
            title = "File System Error"
            message = f"File system error occurred.\n\nFile: {file_path}\nError: {error_msg}"
        else:
            title = "File Error"
            message = f"An error occurred while {operation}.\n\nFile: {file_path}\nError: {error_msg}"
        
        self.show_error_dialog(title, message, "error")
        logger.error(f"File error for {file_path} during {operation}: {error}")


def error_handler(operation_name: str = "operation"):
    """
    Decorator for automatic error handling.
    
    Args:
        operation_name: Name of the operation for error messages
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except MemoryError as e:
                if hasattr(args[0], 'error_handler'):
                    args[0].error_handler.handle_memory_error(e, operation_name)
                else:
                    logger.error(f"Memory error in {operation_name}: {e}")
                return None
            except (FileNotFoundError, PermissionError, OSError) as e:
                if hasattr(args[0], 'error_handler'):
                    args[0].error_handler.handle_file_error(e, operation=operation_name)
                else:
                    logger.error(f"File error in {operation_name}: {e}")
                return None
            except Exception as e:
                logger.error(f"Error in {operation_name}: {e}")
                if hasattr(args[0], 'error_handler'):
                    args[0].error_handler.show_error_dialog(
                        f"Error in {operation_name}",
                        str(e),
                        "error"
                    )
                return None
        return wrapper
    return decorator


class ProgressDialog(ctk.CTkToplevel):
    """Progress dialog for long-running operations."""
    
    def __init__(self, title: str, message: str, parent=None, app=None):
        """
        Initialize progress dialog.
        
        Args:
            title: Dialog title
            message: Progress message
            parent: Parent window
            app: Main application instance
        """
        super().__init__(parent)
        self.app = app
        self.cancelled = False
        
        # Configure window
        self.title(title)
        self.geometry("400x150")
        self.resizable(False, False)
        
        # Make modal
        if parent:
            self.transient(parent)
            self.grab_set()
            
            # Center on parent
            parent.update_idletasks()
            x = parent.winfo_x() + (parent.winfo_width() // 2) - 200
            y = parent.winfo_y() + (parent.winfo_height() // 2) - 75
            self.geometry(f"400x150+{x}+{y}")
        
        # Create UI
        self._create_ui(message)
        
        # Update display
        self.update()
    
    def _create_ui(self, message: str):
        """Create progress dialog UI."""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Message label
        self.message_label = ctk.CTkLabel(
            main_frame,
            text=message,
            font=("Segoe UI", 12)
        )
        self.message_label.pack(pady=(10, 20))
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(main_frame)
        self.progress_bar.pack(fill="x", padx=20, pady=(0, 20))
        self.progress_bar.set(0)
        
        # Cancel button
        self.cancel_btn = ctk.CTkButton(
            main_frame,
            text="Cancel",
            command=self._cancel,
            width=100
        )
        self.cancel_btn.pack(pady=(0, 10))
    
    def update_progress(self, value: float, message: str = None):
        """
        Update progress.
        
        Args:
            value: Progress value (0.0 to 1.0)
            message: Optional new message
        """
        self.progress_bar.set(value)
        if message:
            self.message_label.configure(text=message)
        self.update()
    
    def _cancel(self):
        """Cancel the operation."""
        self.cancelled = True
        self.destroy()
    
    def is_cancelled(self) -> bool:
        """Check if operation was cancelled."""
        return self.cancelled
