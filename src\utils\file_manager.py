"""
File management utilities for ArchPDF - Handle file operations and recent files.
"""

import json
import logging
from pathlib import Path
from typing import List, Optional
from app.config import CONFIG_DIR, MAX_RECENT_FILES

logger = logging.getLogger(__name__)

class FileManager:
    """Manages file operations and recent files list."""
    
    def __init__(self):
        """Initialize file manager."""
        self.recent_files_file = CONFIG_DIR / "recent_files.json"
        self.recent_files: List[str] = []
        
        # Load recent files
        self._load_recent_files()
    
    def _load_recent_files(self):
        """Load recent files from storage."""
        try:
            if self.recent_files_file.exists():
                with open(self.recent_files_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.recent_files = data.get('recent_files', [])
                
                # Validate files still exist
                self.recent_files = [f for f in self.recent_files if Path(f).exists()]
                logger.info(f"Loaded {len(self.recent_files)} recent files")
            
        except Exception as e:
            logger.error(f"Failed to load recent files: {e}")
            self.recent_files = []
    
    def _save_recent_files(self):
        """Save recent files to storage."""
        try:
            data = {'recent_files': self.recent_files}
            
            with open(self.recent_files_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.debug("Recent files saved")
            
        except Exception as e:
            logger.error(f"Failed to save recent files: {e}")
    
    def add_recent_file(self, file_path: str):
        """
        Add a file to the recent files list.
        
        Args:
            file_path: Path to the file
        """
        file_path = str(Path(file_path).resolve())
        
        # Remove if already in list
        if file_path in self.recent_files:
            self.recent_files.remove(file_path)
        
        # Add to beginning
        self.recent_files.insert(0, file_path)
        
        # Limit list size
        if len(self.recent_files) > MAX_RECENT_FILES:
            self.recent_files = self.recent_files[:MAX_RECENT_FILES]
        
        # Save to storage
        self._save_recent_files()
        
        logger.info(f"Added to recent files: {file_path}")
    
    def remove_recent_file(self, file_path: str):
        """
        Remove a file from the recent files list.
        
        Args:
            file_path: Path to the file
        """
        file_path = str(Path(file_path).resolve())
        
        if file_path in self.recent_files:
            self.recent_files.remove(file_path)
            self._save_recent_files()
            logger.info(f"Removed from recent files: {file_path}")
    
    def get_recent_files(self) -> List[str]:
        """
        Get the list of recent files.
        
        Returns:
            List of recent file paths
        """
        # Filter out files that no longer exist
        existing_files = [f for f in self.recent_files if Path(f).exists()]
        
        if len(existing_files) != len(self.recent_files):
            self.recent_files = existing_files
            self._save_recent_files()
        
        return self.recent_files.copy()
    
    def clear_recent_files(self):
        """Clear all recent files."""
        self.recent_files = []
        self._save_recent_files()
        logger.info("Cleared recent files")
    
    def is_valid_pdf_file(self, file_path: str) -> bool:
        """
        Check if a file is a valid PDF file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if valid PDF, False otherwise
        """
        try:
            path = Path(file_path)
            
            # Check if file exists
            if not path.exists():
                return False
            
            # Check file extension
            if path.suffix.lower() != '.pdf':
                return False
            
            # Check if file is readable
            if not path.is_file():
                return False
            
            # Basic PDF header check
            with open(path, 'rb') as f:
                header = f.read(5)
                if header != b'%PDF-':
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating PDF file {file_path}: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Optional[dict]:
        """
        Get information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information or None if error
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                return None
            
            stat = path.stat()
            
            return {
                'name': path.name,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'created': stat.st_ctime,
                'extension': path.suffix.lower(),
                'is_pdf': self.is_valid_pdf_file(file_path)
            }
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            return None
    
    def format_file_size(self, size_bytes: int) -> str:
        """
        Format file size in human-readable format.
        
        Args:
            size_bytes: Size in bytes
            
        Returns:
            Formatted size string
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
