"""
Toolbar component for ArchPDF - Main toolbar with common actions.
"""

import tkinter as tk
import customtkinter as ctk
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class ToolBar(ctk.CTkFrame):
    """Main toolbar with common PDF operations."""
    
    def __init__(self, parent, app):
        """
        Initialize the toolbar.
        
        Args:
            parent: Parent widget
            app: Main application instance
        """
        super().__init__(parent, **app.theme_manager.get_frame_style("secondary"))
        self.app = app
        
        # Create toolbar buttons
        self._create_buttons()
        
        # Initial state
        self.update_state(False)
    
    def _create_buttons(self):
        """Create toolbar buttons."""
        # Configure grid
        self.grid_columnconfigure(100, weight=1)  # Spacer column
        
        # File operations
        self.open_btn = ctk.CTkButton(
            self,
            text="Open",
            width=80,
            command=self.app.open_file,
            **self.app.theme_manager.get_button_style("primary")
        )
        self.open_btn.grid(row=0, column=0, padx=(10, 5), pady=10)
        
        # Separator
        sep1 = self.app.theme_manager.create_separator(self, "vertical")
        sep1.grid(row=0, column=1, sticky="ns", padx=5, pady=15)
        
        # Navigation
        self.prev_btn = ctk.CTkButton(
            self,
            text="◀",
            width=40,
            command=self.app.previous_page,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.prev_btn.grid(row=0, column=2, padx=5, pady=10)
        
        # Page info
        self.page_frame = ctk.CTkFrame(self, **self.app.theme_manager.get_frame_style("tertiary"))
        self.page_frame.grid(row=0, column=3, padx=5, pady=10)
        
        self.page_entry = ctk.CTkEntry(
            self.page_frame,
            width=60,
            justify="center",
            **self.app.theme_manager.get_entry_style()
        )
        self.page_entry.pack(side="left", padx=5, pady=5)
        self.page_entry.bind("<Return>", self._on_page_entry)
        
        self.page_label = ctk.CTkLabel(
            self.page_frame,
            text="/ 0",
            **self.app.theme_manager.get_label_style("secondary")
        )
        self.page_label.pack(side="left", padx=(0, 5), pady=5)
        
        self.next_btn = ctk.CTkButton(
            self,
            text="▶",
            width=40,
            command=self.app.next_page,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.next_btn.grid(row=0, column=4, padx=5, pady=10)
        
        # Separator
        sep2 = self.app.theme_manager.create_separator(self, "vertical")
        sep2.grid(row=0, column=5, sticky="ns", padx=5, pady=15)
        
        # Zoom controls
        self.zoom_out_btn = ctk.CTkButton(
            self,
            text="−",
            width=40,
            command=self.app.zoom_out,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.zoom_out_btn.grid(row=0, column=6, padx=5, pady=10)
        
        self.zoom_label = ctk.CTkLabel(
            self,
            text="100%",
            width=60,
            **self.app.theme_manager.get_label_style("primary")
        )
        self.zoom_label.grid(row=0, column=7, padx=5, pady=10)
        
        self.zoom_in_btn = ctk.CTkButton(
            self,
            text="+",
            width=40,
            command=self.app.zoom_in,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.zoom_in_btn.grid(row=0, column=8, padx=5, pady=10)
        
        # Fit controls
        self.fit_width_btn = ctk.CTkButton(
            self,
            text="Fit Width",
            width=80,
            command=self.app.zoom_fit_width,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.fit_width_btn.grid(row=0, column=9, padx=5, pady=10)
        
        self.fit_page_btn = ctk.CTkButton(
            self,
            text="Fit Page",
            width=80,
            command=self.app.zoom_fit_page,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.fit_page_btn.grid(row=0, column=10, padx=5, pady=10)
        
        # Separator
        sep3 = self.app.theme_manager.create_separator(self, "vertical")
        sep3.grid(row=0, column=11, sticky="ns", padx=5, pady=15)
        
        # Search
        self.search_btn = ctk.CTkButton(
            self,
            text="Search",
            width=80,
            command=self.app.show_search_dialog,
            **self.app.theme_manager.get_button_style("accent")
        )
        self.search_btn.grid(row=0, column=12, padx=5, pady=10)
        
        # Spacer (column 100 has weight=1)
        
        # View controls
        self.sidebar_btn = ctk.CTkButton(
            self,
            text="Sidebar",
            width=80,
            command=self.app.toggle_sidebar,
            **self.app.theme_manager.get_button_style("secondary")
        )
        self.sidebar_btn.grid(row=0, column=101, padx=(5, 10), pady=10)
    
    def _on_page_entry(self, event):
        """Handle page entry return key."""
        try:
            page_num = int(self.page_entry.get())
            self.app.go_to_page(page_num)
        except ValueError:
            # Invalid page number, reset to current page
            if self.app.pdf_engine.is_document_loaded():
                current_page = self.app.pdf_engine.get_current_page() + 1
                self.page_entry.delete(0, "end")
                self.page_entry.insert(0, str(current_page))
    
    def update_state(self, has_document: bool):
        """
        Update toolbar state based on document availability.
        
        Args:
            has_document: Whether a document is loaded
        """
        # Navigation buttons
        self.prev_btn.configure(state="normal" if has_document else "disabled")
        self.next_btn.configure(state="normal" if has_document else "disabled")
        
        # Page entry
        self.page_entry.configure(state="normal" if has_document else "disabled")
        
        # Zoom controls
        self.zoom_out_btn.configure(state="normal" if has_document else "disabled")
        self.zoom_in_btn.configure(state="normal" if has_document else "disabled")
        self.fit_width_btn.configure(state="normal" if has_document else "disabled")
        self.fit_page_btn.configure(state="normal" if has_document else "disabled")
        
        # Search
        self.search_btn.configure(state="normal" if has_document else "disabled")
        
        # Update page info
        if has_document:
            current_page = self.app.pdf_engine.get_current_page() + 1
            total_pages = self.app.pdf_engine.page_count
            
            self.page_entry.delete(0, "end")
            self.page_entry.insert(0, str(current_page))
            self.page_label.configure(text=f"/ {total_pages}")
            
            # Update zoom level
            zoom_level = self.app.pdf_viewer.get_zoom_level()
            self.zoom_label.configure(text=f"{zoom_level}%")
        else:
            self.page_entry.delete(0, "end")
            self.page_label.configure(text="/ 0")
            self.zoom_label.configure(text="100%")
    
    def update_page_info(self, current_page: int, total_pages: int):
        """
        Update page information display.
        
        Args:
            current_page: Current page number (1-based)
            total_pages: Total number of pages
        """
        self.page_entry.delete(0, "end")
        self.page_entry.insert(0, str(current_page))
        self.page_label.configure(text=f"/ {total_pages}")
    
    def update_zoom_info(self, zoom_level: int):
        """
        Update zoom level display.
        
        Args:
            zoom_level: Current zoom level percentage
        """
        self.zoom_label.configure(text=f"{zoom_level}%")
