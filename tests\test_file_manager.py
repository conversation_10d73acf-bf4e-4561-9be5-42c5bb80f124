"""
Test cases for file manager functionality.
"""

import unittest
import tempfile
import os
import json
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils.file_manager import FileManager

class TestFileManager(unittest.TestCase):
    """Test cases for FileManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary config directory
        self.temp_dir = tempfile.mkdtemp()
        
        # Patch CONFIG_DIR for testing
        import app.config
        self.original_config_dir = app.config.CONFIG_DIR
        app.config.CONFIG_DIR = Path(self.temp_dir)
        
        self.file_manager = FileManager()
    
    def tearDown(self):
        """Clean up after tests."""
        # Restore original CONFIG_DIR
        import app.config
        app.config.CONFIG_DIR = self.original_config_dir
        
        # Clean up temp directory
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_file_manager_initialization(self):
        """Test file manager initialization."""
        self.assertIsNotNone(self.file_manager)
        self.assertIsInstance(self.file_manager.recent_files, list)
    
    def test_add_recent_file(self):
        """Test adding files to recent files list."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as f:
            test_file = f.name
        
        try:
            self.file_manager.add_recent_file(test_file)
            recent_files = self.file_manager.get_recent_files()
            self.assertIn(test_file, recent_files)
            self.assertEqual(recent_files[0], test_file)  # Should be first
        finally:
            os.remove(test_file)
    
    def test_remove_recent_file(self):
        """Test removing files from recent files list."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as f:
            test_file = f.name
        
        try:
            # Add and then remove
            self.file_manager.add_recent_file(test_file)
            self.file_manager.remove_recent_file(test_file)
            recent_files = self.file_manager.get_recent_files()
            self.assertNotIn(test_file, recent_files)
        finally:
            os.remove(test_file)
    
    def test_clear_recent_files(self):
        """Test clearing all recent files."""
        # Add some files first
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as f1:
            test_file1 = f1.name
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as f2:
            test_file2 = f2.name
        
        try:
            self.file_manager.add_recent_file(test_file1)
            self.file_manager.add_recent_file(test_file2)
            
            self.file_manager.clear_recent_files()
            recent_files = self.file_manager.get_recent_files()
            self.assertEqual(len(recent_files), 0)
        finally:
            os.remove(test_file1)
            os.remove(test_file2)
    
    def test_is_valid_pdf_file_nonexistent(self):
        """Test PDF validation with non-existent file."""
        result = self.file_manager.is_valid_pdf_file("nonexistent.pdf")
        self.assertFalse(result)
    
    def test_is_valid_pdf_file_wrong_extension(self):
        """Test PDF validation with wrong extension."""
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as f:
            test_file = f.name
        
        try:
            result = self.file_manager.is_valid_pdf_file(test_file)
            self.assertFalse(result)
        finally:
            os.remove(test_file)
    
    def test_is_valid_pdf_file_invalid_content(self):
        """Test PDF validation with invalid content."""
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as f:
            f.write(b"This is not a PDF")
            test_file = f.name
        
        try:
            result = self.file_manager.is_valid_pdf_file(test_file)
            self.assertFalse(result)
        finally:
            os.remove(test_file)
    
    def test_get_file_info_nonexistent(self):
        """Test getting file info for non-existent file."""
        info = self.file_manager.get_file_info("nonexistent.pdf")
        self.assertIsNone(info)
    
    def test_get_file_info_existing(self):
        """Test getting file info for existing file."""
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as f:
            f.write(b"test content")
            test_file = f.name
        
        try:
            info = self.file_manager.get_file_info(test_file)
            self.assertIsNotNone(info)
            self.assertIn('name', info)
            self.assertIn('size', info)
            self.assertIn('extension', info)
            self.assertEqual(info['extension'], '.pdf')
        finally:
            os.remove(test_file)
    
    def test_format_file_size(self):
        """Test file size formatting."""
        self.assertEqual(self.file_manager.format_file_size(0), "0 B")
        self.assertEqual(self.file_manager.format_file_size(1024), "1.0 KB")
        self.assertEqual(self.file_manager.format_file_size(1024 * 1024), "1.0 MB")
        self.assertEqual(self.file_manager.format_file_size(1024 * 1024 * 1024), "1.0 GB")
    
    def test_recent_files_limit(self):
        """Test that recent files list respects the limit."""
        from app.config import MAX_RECENT_FILES
        
        # Create more files than the limit
        test_files = []
        for i in range(MAX_RECENT_FILES + 5):
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as f:
                test_files.append(f.name)
        
        try:
            # Add all files
            for file_path in test_files:
                self.file_manager.add_recent_file(file_path)
            
            # Check that list is limited
            recent_files = self.file_manager.get_recent_files()
            self.assertLessEqual(len(recent_files), MAX_RECENT_FILES)
        finally:
            for file_path in test_files:
                if os.path.exists(file_path):
                    os.remove(file_path)

if __name__ == '__main__':
    unittest.main()
