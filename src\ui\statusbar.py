"""
Status bar component for ArchPDF - Shows current page, zoom level, and status information.
"""

import customtkinter as ctk
import logging

logger = logging.getLogger(__name__)

class StatusBar(ctk.CTkFrame):
    """Status bar showing page info, zoom level, and status messages."""
    
    def __init__(self, parent, app):
        """
        Initialize the status bar.
        
        Args:
            parent: Parent widget
            app: Main application instance
        """
        super().__init__(parent, **app.theme_manager.get_frame_style("secondary"))
        self.app = app
        
        # Create status bar elements
        self._create_elements()
    
    def _create_elements(self):
        """Create status bar elements."""
        # Configure grid
        self.grid_columnconfigure(1, weight=1)  # Status message column expands
        
        # Page information
        self.page_info_label = ctk.CTkLabel(
            self,
            text="No document",
            **self.app.theme_manager.get_label_style("secondary")
        )
        self.page_info_label.grid(row=0, column=0, padx=(10, 5), pady=5, sticky="w")
        
        # Status message (expandable)
        self.status_label = ctk.CTkLabel(
            self,
            text="Ready",
            **self.app.theme_manager.get_label_style("secondary")
        )
        self.status_label.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # Zoom information
        self.zoom_info_label = ctk.CTkLabel(
            self,
            text="100%",
            **self.app.theme_manager.get_label_style("secondary")
        )
        self.zoom_info_label.grid(row=0, column=2, padx=(5, 10), pady=5, sticky="e")
    
    def update_page_info(self, current_page: int, total_pages: int):
        """
        Update page information display.
        
        Args:
            current_page: Current page number (1-based)
            total_pages: Total number of pages
        """
        if total_pages > 0:
            text = f"Page {current_page} of {total_pages}"
        else:
            text = "No document"
        
        self.page_info_label.configure(text=text)
    
    def update_zoom_info(self, zoom_level: int):
        """
        Update zoom level display.
        
        Args:
            zoom_level: Current zoom level percentage
        """
        self.zoom_info_label.configure(text=f"{zoom_level}%")
    
    def set_status(self, message: str):
        """
        Set status message.
        
        Args:
            message: Status message to display
        """
        self.status_label.configure(text=message)
        logger.debug(f"Status: {message}")
    
    def clear_status(self):
        """Clear status message."""
        self.status_label.configure(text="Ready")
