"""
PDF Viewer Component for ArchPDF - Main PDF viewing widget with zoom and navigation.
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
from PIL import Image, ImageTk
import io
from typing import Optional, Tuple
import logging

from app.config import MIN_ZOOM, MAX_ZOOM, ZOOM_STEP

logger = logging.getLogger(__name__)

class PDFViewer(ctk.CTkFrame):
    """Main PDF viewing component with zoom and navigation capabilities."""
    
    def __init__(self, parent, app):
        """
        Initialize the PDF viewer.
        
        Args:
            parent: Parent widget
            app: Main application instance
        """
        super().__init__(parent)
        self.app = app
        self.pdf_engine = app.pdf_engine
        
        # Viewer state
        self.zoom_level = 100  # Percentage
        self.fit_mode = "none"  # none, fit_width, fit_page
        self.current_image = None
        self.current_photo = None
        
        # Create UI
        self._create_ui()
        
        # Bind events
        self._bind_events()
    
    def _create_ui(self):
        """Create the viewer UI."""
        # Configure grid
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
        # Create scrollable frame
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self,
            **self.app.theme_manager.get_frame_style("secondary")
        )
        self.scrollable_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        
        # Configure scrollable frame
        self.scrollable_frame.grid_rowconfigure(0, weight=1)
        self.scrollable_frame.grid_columnconfigure(0, weight=1)
        
        # Create canvas for PDF display
        self.canvas = tk.Canvas(
            self.scrollable_frame,
            bg=self.app.theme_manager.get_color("bg_primary"),
            highlightthickness=0,
            relief="flat"
        )
        self.canvas.grid(row=0, column=0, sticky="nsew")
        
        # Create placeholder label
        self.placeholder_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="No PDF loaded\n\nUse File > Open to load a PDF document",
            **self.app.theme_manager.get_label_style("secondary")
        )
        self.placeholder_label.grid(row=0, column=0, sticky="nsew")
    
    def _bind_events(self):
        """Bind mouse and keyboard events."""
        # Mouse wheel for zooming (with Ctrl)
        self.canvas.bind("<Control-MouseWheel>", self._on_ctrl_mousewheel)
        
        # Mouse wheel for scrolling
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        
        # Mouse drag for panning
        self.canvas.bind("<Button-1>", self._on_mouse_press)
        self.canvas.bind("<B1-Motion>", self._on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_mouse_release)
        
        # Canvas resize
        self.canvas.bind("<Configure>", self._on_canvas_configure)
        
        # Focus for keyboard events
        self.canvas.bind("<Button-1>", lambda e: self.canvas.focus_set())
        
        # Variables for mouse dragging
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.is_dragging = False
    
    def load_document(self):
        """Load the current document into the viewer."""
        if not self.pdf_engine.is_document_loaded():
            self.clear()
            return
        
        # Hide placeholder
        self.placeholder_label.grid_remove()
        
        # Show canvas
        self.canvas.grid(row=0, column=0, sticky="nsew")
        
        # Load first page
        self.update_page()
        
        logger.info("Document loaded in viewer")
    
    def clear(self):
        """Clear the viewer."""
        # Clear canvas
        self.canvas.delete("all")
        self.current_image = None
        self.current_photo = None
        
        # Hide canvas and show placeholder
        self.canvas.grid_remove()
        self.placeholder_label.grid(row=0, column=0, sticky="nsew")
        
        logger.info("Viewer cleared")
    
    def update_page(self):
        """Update the displayed page."""
        if not self.pdf_engine.is_document_loaded():
            return
        
        current_page = self.pdf_engine.get_current_page()
        
        # Calculate zoom factor
        zoom_factor = self.zoom_level / 100.0
        
        # Handle fit modes
        if self.fit_mode in ["fit_width", "fit_page"]:
            zoom_factor = self._calculate_fit_zoom()
        
        # Get page image
        image_data = self.pdf_engine.get_page_image(current_page, zoom_factor)
        
        if image_data:
            self._display_image(image_data)
        
        logger.debug(f"Updated page {current_page + 1} with zoom {zoom_factor:.2f}")
    
    def _display_image(self, image_data: bytes):
        """Display image data on the canvas."""
        try:
            # Load image
            image = Image.open(io.BytesIO(image_data))
            self.current_image = image
            
            # Convert to PhotoImage
            self.current_photo = ImageTk.PhotoImage(image)
            
            # Clear canvas
            self.canvas.delete("all")
            
            # Calculate canvas size
            canvas_width = max(image.width + 40, self.canvas.winfo_width())
            canvas_height = max(image.height + 40, self.canvas.winfo_height())
            
            # Configure canvas scroll region
            self.canvas.configure(scrollregion=(0, 0, canvas_width, canvas_height))
            
            # Center image on canvas
            x = canvas_width // 2
            y = canvas_height // 2
            
            # Display image
            self.canvas.create_image(x, y, image=self.current_photo, anchor="center")
            
            # Update canvas size
            self.canvas.configure(width=canvas_width, height=canvas_height)
            
        except Exception as e:
            logger.error(f"Failed to display image: {e}")
    
    def _calculate_fit_zoom(self) -> float:
        """Calculate zoom factor for fit modes."""
        if not self.pdf_engine.is_document_loaded():
            return 1.0
        
        current_page = self.pdf_engine.get_current_page()
        page_size = self.pdf_engine.get_page_size(current_page)
        
        if not page_size:
            return 1.0
        
        page_width, page_height = page_size
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            return 1.0
        
        # Calculate zoom factors
        width_zoom = (canvas_width - 40) / page_width
        height_zoom = (canvas_height - 40) / page_height
        
        if self.fit_mode == "fit_width":
            zoom_factor = width_zoom
        elif self.fit_mode == "fit_page":
            zoom_factor = min(width_zoom, height_zoom)
        else:
            zoom_factor = 1.0
        
        # Clamp zoom factor
        zoom_factor = max(MIN_ZOOM / 100.0, min(MAX_ZOOM / 100.0, zoom_factor))
        
        return zoom_factor
    
    def zoom_in(self):
        """Zoom in."""
        if self.zoom_level < MAX_ZOOM:
            self.zoom_level = min(MAX_ZOOM, self.zoom_level + ZOOM_STEP)
            self.fit_mode = "none"
            self.update_page()
    
    def zoom_out(self):
        """Zoom out."""
        if self.zoom_level > MIN_ZOOM:
            self.zoom_level = max(MIN_ZOOM, self.zoom_level - ZOOM_STEP)
            self.fit_mode = "none"
            self.update_page()
    
    def zoom_actual_size(self):
        """Set zoom to actual size (100%)."""
        self.zoom_level = 100
        self.fit_mode = "none"
        self.update_page()
    
    def zoom_fit_width(self):
        """Fit page width to window."""
        self.fit_mode = "fit_width"
        self.update_page()
        # Update zoom level for display
        self.zoom_level = int(self._calculate_fit_zoom() * 100)
    
    def zoom_fit_page(self):
        """Fit entire page to window."""
        self.fit_mode = "fit_page"
        self.update_page()
        # Update zoom level for display
        self.zoom_level = int(self._calculate_fit_zoom() * 100)
    
    def set_zoom(self, zoom_level: int):
        """Set specific zoom level."""
        self.zoom_level = max(MIN_ZOOM, min(MAX_ZOOM, zoom_level))
        self.fit_mode = "none"
        self.update_page()
    
    def get_zoom_level(self) -> int:
        """Get current zoom level."""
        return self.zoom_level
    
    def _on_ctrl_mousewheel(self, event):
        """Handle Ctrl+mouse wheel for zooming."""
        if event.delta > 0:
            self.zoom_in()
        else:
            self.zoom_out()
    
    def _on_mousewheel(self, event):
        """Handle mouse wheel for scrolling."""
        # Scroll vertically
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def _on_mouse_press(self, event):
        """Handle mouse press for dragging."""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        self.is_dragging = False
        self.canvas.configure(cursor="hand2")
    
    def _on_mouse_drag(self, event):
        """Handle mouse drag for panning."""
        if not self.is_dragging:
            self.is_dragging = True
        
        # Calculate drag distance
        dx = event.x - self.drag_start_x
        dy = event.y - self.drag_start_y
        
        # Pan the view
        self.canvas.xview_scroll(-dx, "units")
        self.canvas.yview_scroll(-dy, "units")
        
        # Update drag start position
        self.drag_start_x = event.x
        self.drag_start_y = event.y
    
    def _on_mouse_release(self, event):
        """Handle mouse release."""
        self.is_dragging = False
        self.canvas.configure(cursor="")
    
    def _on_canvas_configure(self, event):
        """Handle canvas resize."""
        # Update page if in fit mode
        if self.fit_mode in ["fit_width", "fit_page"]:
            self.update_page()
