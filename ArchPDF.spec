# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# Get the current directory
current_dir = Path(SPECPATH).parent

# Define data files
datas = []

# Add assets if they exist
assets_dir = current_dir / 'assets'
if assets_dir.exists():
    for root, dirs, files in os.walk(assets_dir):
        for file in files:
            file_path = Path(root) / file
            rel_path = file_path.relative_to(current_dir)
            dest_path = str(rel_path.parent)
            datas.append((str(file_path), dest_path))

# Add src directory
src_dir = current_dir / 'src'
if src_dir.exists():
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                rel_path = file_path.relative_to(current_dir)
                dest_path = str(rel_path.parent)
                datas.append((str(file_path), dest_path))

a = Analysis(
    ['main.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'tkinter',
        'customtkinter',
        'fitz',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageDraw',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=2,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ArchPDF',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(current_dir / 'assets' / 'icons' / 'app_icon.ico') if (current_dir / 'assets' / 'icons' / 'app_icon.ico').exists() else None,
)
