"""
Menu bar component for ArchPDF - Main application menu.
"""

import tkinter as tk
from tkinter import messagebox
import logging

logger = logging.getLogger(__name__)

class MenuBar:
    """Main application menu bar."""
    
    def __init__(self, root, app):
        """
        Initialize the menu bar.
        
        Args:
            root: Root window
            app: Main application instance
        """
        self.root = root
        self.app = app
        
        # Create menu bar with better contrast
        self.menubar = tk.Menu(root,
                              bg=self.app.theme_manager.get_color("bg_primary"),
                              fg=self.app.theme_manager.get_color("fg_primary"),
                              activebackground=self.app.theme_manager.get_color("accent_primary"),
                              activeforeground="#ffffff",
                              selectcolor=self.app.theme_manager.get_color("accent_primary"),
                              relief="flat",
                              borderwidth=0)
        
        # Create menus
        self._create_file_menu()
        self._create_view_menu()
        self._create_tools_menu()
        self._create_help_menu()
        
        # Set menu bar
        root.config(menu=self.menubar)
        
        # Initial state
        self.update_state(False)
    
    def _create_file_menu(self):
        """Create File menu."""
        self.file_menu = tk.Menu(self.menubar, tearoff=0,
                                bg=self.app.theme_manager.get_color("bg_primary"),
                                fg=self.app.theme_manager.get_color("fg_primary"),
                                activebackground=self.app.theme_manager.get_color("accent_primary"),
                                activeforeground="#ffffff",
                                selectcolor=self.app.theme_manager.get_color("accent_primary"),
                                relief="flat",
                                borderwidth=1,
                                font=("Segoe UI", 9))
        
        self.file_menu.add_command(label="Open...", command=self.app.open_file, accelerator="Ctrl+O")
        self.file_menu.add_separator()
        
        # Recent files submenu
        self.recent_menu = tk.Menu(self.file_menu, tearoff=0,
                                  bg=self.app.theme_manager.get_color("bg_primary"),
                                  fg=self.app.theme_manager.get_color("fg_primary"),
                                  activebackground=self.app.theme_manager.get_color("accent_primary"),
                                  activeforeground="#ffffff",
                                  selectcolor=self.app.theme_manager.get_color("accent_primary"),
                                  relief="flat",
                                  borderwidth=1,
                                  font=("Segoe UI", 9))
        self.file_menu.add_cascade(label="Recent Files", menu=self.recent_menu)
        self._update_recent_files()
        
        self.file_menu.add_separator()
        self.file_menu.add_command(label="Close", command=self.app.close_file, state="disabled")
        self.file_menu.add_separator()
        self.file_menu.add_command(label="Exit", command=self.app.on_closing, accelerator="Ctrl+Q")
        
        self.menubar.add_cascade(label="File", menu=self.file_menu)
    
    def _create_view_menu(self):
        """Create View menu."""
        self.view_menu = tk.Menu(self.menubar, tearoff=0,
                                bg=self.app.theme_manager.get_color("bg_secondary"),
                                fg=self.app.theme_manager.get_color("fg_primary"),
                                activebackground=self.app.theme_manager.get_color("hover"),
                                activeforeground=self.app.theme_manager.get_color("fg_primary"))
        
        # Zoom submenu
        self.zoom_menu = tk.Menu(self.view_menu, tearoff=0,
                                bg=self.app.theme_manager.get_color("bg_secondary"),
                                fg=self.app.theme_manager.get_color("fg_primary"),
                                activebackground=self.app.theme_manager.get_color("hover"),
                                activeforeground=self.app.theme_manager.get_color("fg_primary"))
        
        self.zoom_menu.add_command(label="Zoom In", command=self.app.zoom_in, 
                                  accelerator="Ctrl++", state="disabled")
        self.zoom_menu.add_command(label="Zoom Out", command=self.app.zoom_out, 
                                  accelerator="Ctrl+-", state="disabled")
        self.zoom_menu.add_command(label="Actual Size", command=self.app.zoom_actual_size, 
                                  accelerator="Ctrl+0", state="disabled")
        self.zoom_menu.add_separator()
        self.zoom_menu.add_command(label="Fit Width", command=self.app.zoom_fit_width, state="disabled")
        self.zoom_menu.add_command(label="Fit Page", command=self.app.zoom_fit_page, state="disabled")
        
        self.view_menu.add_cascade(label="Zoom", menu=self.zoom_menu)
        self.view_menu.add_separator()
        
        # Navigation
        self.view_menu.add_command(label="Next Page", command=self.app.next_page, 
                                  accelerator="Page Down", state="disabled")
        self.view_menu.add_command(label="Previous Page", command=self.app.previous_page, 
                                  accelerator="Page Up", state="disabled")
        self.view_menu.add_command(label="Go to Page...", command=self._show_goto_dialog, state="disabled")
        
        self.view_menu.add_separator()
        
        # View options
        self.view_menu.add_command(label="Toggle Sidebar", command=self.app.toggle_sidebar)
        self.view_menu.add_command(label="Fullscreen", command=self.app.toggle_fullscreen, accelerator="F11")
        
        self.menubar.add_cascade(label="View", menu=self.view_menu)
    
    def _create_tools_menu(self):
        """Create Tools menu."""
        self.tools_menu = tk.Menu(self.menubar, tearoff=0,
                                 bg=self.app.theme_manager.get_color("bg_secondary"),
                                 fg=self.app.theme_manager.get_color("fg_primary"),
                                 activebackground=self.app.theme_manager.get_color("hover"),
                                 activeforeground=self.app.theme_manager.get_color("fg_primary"))
        
        self.tools_menu.add_command(label="Find...", command=self.app.show_search_dialog,
                                   accelerator="Ctrl+F", state="disabled")
        self.tools_menu.add_separator()
        self.tools_menu.add_command(label="Document Properties", command=self._show_properties, state="disabled")
        self.tools_menu.add_separator()
        self.tools_menu.add_command(label="Settings...", command=self._show_settings)
        
        self.menubar.add_cascade(label="Tools", menu=self.tools_menu)
    
    def _create_help_menu(self):
        """Create Help menu."""
        self.help_menu = tk.Menu(self.menubar, tearoff=0,
                                bg=self.app.theme_manager.get_color("bg_secondary"),
                                fg=self.app.theme_manager.get_color("fg_primary"),
                                activebackground=self.app.theme_manager.get_color("hover"),
                                activeforeground=self.app.theme_manager.get_color("fg_primary"))
        
        self.help_menu.add_command(label="Keyboard Shortcuts", command=self._show_shortcuts)
        self.help_menu.add_separator()
        self.help_menu.add_command(label="About ArchPDF", command=self.app.show_about)
        
        self.menubar.add_cascade(label="Help", menu=self.help_menu)
    
    def _update_recent_files(self):
        """Update recent files menu."""
        # Clear existing items
        self.recent_menu.delete(0, "end")
        
        # Get recent files
        recent_files = self.app.file_manager.get_recent_files()
        
        if recent_files:
            for file_path in recent_files:
                filename = file_path.split("/")[-1] if "/" in file_path else file_path.split("\\")[-1]
                self.recent_menu.add_command(
                    label=filename,
                    command=lambda path=file_path: self.app.open_file(path)
                )
            self.recent_menu.add_separator()
            self.recent_menu.add_command(label="Clear Recent Files", 
                                        command=self.app.file_manager.clear_recent_files)
        else:
            self.recent_menu.add_command(label="(No recent files)", state="disabled")
    
    def _show_goto_dialog(self):
        """Show go to page dialog."""
        if not self.app.pdf_engine.is_document_loaded():
            return
        
        # Simple input dialog
        from tkinter import simpledialog
        page_num = simpledialog.askinteger(
            "Go to Page",
            f"Enter page number (1-{self.app.pdf_engine.page_count}):",
            minvalue=1,
            maxvalue=self.app.pdf_engine.page_count
        )
        
        if page_num:
            self.app.go_to_page(page_num)
    
    def _show_properties(self):
        """Show document properties dialog."""
        if not self.app.pdf_engine.is_document_loaded():
            return
        
        doc_info = self.app.pdf_engine.get_document_info()
        
        properties_text = f"""Document Properties

Title: {doc_info.get('title', 'N/A')}
Author: {doc_info.get('author', 'N/A')}
Subject: {doc_info.get('subject', 'N/A')}
Creator: {doc_info.get('creator', 'N/A')}
Producer: {doc_info.get('producer', 'N/A')}

Pages: {doc_info.get('page_count', 0)}
File Size: {doc_info.get('file_size', 0):,} bytes

Created: {doc_info.get('creation_date', 'N/A')}
Modified: {doc_info.get('modification_date', 'N/A')}"""
        
        messagebox.showinfo("Document Properties", properties_text)

    def _show_settings(self):
        """Show settings dialog."""
        from app.settings import SettingsDialog
        SettingsDialog(self.root, self.app)
    
    def _show_shortcuts(self):
        """Show keyboard shortcuts dialog."""
        shortcuts_text = """Keyboard Shortcuts

File Operations:
Ctrl+O          Open file
Ctrl+Q          Quit application

Navigation:
Page Up         Previous page
Page Down       Next page

Zoom:
Ctrl++          Zoom in
Ctrl+-          Zoom out
Ctrl+0          Actual size

Search:
Ctrl+F          Find text

View:
F11             Toggle fullscreen"""
        
        messagebox.showinfo("Keyboard Shortcuts", shortcuts_text)
    
    def update_state(self, has_document: bool):
        """
        Update menu state based on document availability.
        
        Args:
            has_document: Whether a document is loaded
        """
        # File menu
        self.file_menu.entryconfig("Close", state="normal" if has_document else "disabled")
        
        # View menu - zoom items
        state = "normal" if has_document else "disabled"
        for i in range(self.zoom_menu.index("end") + 1):
            try:
                self.zoom_menu.entryconfig(i, state=state)
            except:
                pass
        
        # View menu - navigation items
        self.view_menu.entryconfig("Next Page", state=state)
        self.view_menu.entryconfig("Previous Page", state=state)
        self.view_menu.entryconfig("Go to Page...", state=state)
        
        # Tools menu
        self.tools_menu.entryconfig("Find...", state=state)
        self.tools_menu.entryconfig("Document Properties", state=state)
        
        # Update recent files
        self._update_recent_files()
