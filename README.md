# ArchPDF - Professional PDF Reader

A modern, standalone PDF reader application for Windows with a beautiful dark theme and professional bicolor icons.

![ArchPDF Screenshot](assets/icons/app_icon.png)

## 🚀 Quick Start

### Download and Run
1. Download `ArchPDF.exe` from the [releases](dist/)
2. Double-click to run - no installation required!
3. Open a PDF file and start reading

### System Requirements
- Windows 10 or later (64-bit)
- 4 GB RAM minimum
- 100 MB free disk space

## ✨ Key Features

### 📖 PDF Viewing
- **High-Quality Rendering**: Crisp, accurate PDF display
- **Smart Zoom**: 25%-500% zoom with fit-to-width/page modes
- **Smooth Navigation**: Page-by-page or direct page jumping
- **Bookmarks Support**: Navigate document outline automatically

### 🎨 Modern Interface
- **Dark Theme**: Professional dark color scheme
- **Bicolor Icons**: Consistent blue and orange accent colors
- **Responsive Layout**: Adapts to different screen sizes
- **Customizable UI**: Toggle sidebar, toolbar, and status bar

### 🔍 Advanced Search
- **Full-Text Search**: Find text across entire documents
- **Smart Options**: Case-sensitive, whole words, regex support
- **Result Navigation**: Browse through search results easily
- **Context Preview**: See text around search matches

### 📁 File Management
- **Recent Files**: Quick access to recently opened documents
- **File Validation**: Automatic PDF integrity checking
- **Error Handling**: Graceful handling of corrupted files
- **Large File Support**: Efficient handling of big documents

### ⚡ Performance
- **Fast Rendering**: Optimized PDF display engine
- **Memory Efficient**: Smart memory management
- **Responsive UI**: Smooth interaction even with large files
- **Background Processing**: Non-blocking operations

## 🛠 Technical Stack

- **Python 3.8+**: Core application language
- **CustomTkinter**: Modern GUI framework with dark theme
- **PyMuPDF (fitz)**: Industry-standard PDF processing
- **PyInstaller**: Standalone executable packaging
- **Pillow**: Image processing for icons and graphics

## Project Structure

```
archpdf_v2/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── build_exe.py           # PyInstaller build script
├── README.md              # This file
├── src/                   # Source code
│   ├── app/               # Main application
│   │   ├── main_window.py # Main application window
│   │   ├── config.py      # Configuration settings
│   │   └── settings.py    # Settings management
│   ├── ui/                # UI components
│   │   ├── toolbar.py     # Toolbar implementation
│   │   ├── menubar.py     # Menu bar implementation
│   │   ├── statusbar.py   # Status bar implementation
│   │   ├── sidebar.py     # Side panel implementation
│   │   └── theme.py       # Theme management
│   ├── pdf/               # PDF processing
│   │   ├── engine.py      # PDF engine
│   │   ├── viewer.py      # PDF viewer component
│   │   └── search.py      # Search functionality
│   └── utils/             # Utilities
│       ├── file_manager.py # File operations
│       ├── settings_manager.py # Settings persistence
│       └── icon_manager.py # Icon management
├── assets/                # Application assets
│   ├── icons/             # Bicolor icon set
│   └── themes/            # Theme definitions
└── dist/                  # Built executables (generated)
```

## Installation and Setup

### Development Setup

1. Clone or download the project
2. Install Python 3.8 or higher
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Run the application:
   ```bash
   python main.py
   ```

### Building Standalone Executable

1. Install PyInstaller:
   ```bash
   pip install pyinstaller
   ```
2. Run the build script:
   ```bash
   python build_exe.py
   ```
3. Find the executable in the `dist/` directory

## 📖 Documentation

- **[Installation Guide](INSTALL.md)**: Detailed setup instructions
- **[User Guide](USER_GUIDE.md)**: Complete usage documentation
- **[Features Overview](FEATURES.md)**: Comprehensive feature list

## 🚀 Quick Usage

1. **Open a PDF**: File > Open or Ctrl+O
2. **Navigate**: Use toolbar buttons or Page Up/Down keys
3. **Zoom**: Ctrl+Plus/Minus or mouse wheel with Ctrl
4. **Search**: Ctrl+F to find text in the document
5. **Customize**: Tools > Settings to configure preferences

## ⌨️ Keyboard Shortcuts

| Action | Shortcut |
|--------|----------|
| Open file | `Ctrl+O` |
| Find text | `Ctrl+F` |
| Quit application | `Ctrl+Q` |
| Previous/Next page | `Page Up/Down` |
| Zoom in/out | `Ctrl+±` |
| Actual size | `Ctrl+0` |
| Toggle fullscreen | `F11` |

## 🧪 Testing

Run the test suite to verify functionality:

```bash
python run_tests.py
```

For specific tests:
```bash
python run_tests.py test_pdf_engine
python run_tests.py test_file_manager
```

## 🏗️ Building from Source

### Prerequisites
- Python 3.8+
- pip package manager

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd archpdf_v2

# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

### Build Executable
```bash
# Install PyInstaller
pip install pyinstaller

# Build standalone executable
python build_exe.py

# Find executable in dist/ folder
```

## 📁 Project Structure

```
archpdf_v2/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── build_exe.py           # PyInstaller build script
├── ArchPDF.spec           # PyInstaller configuration
├── src/                   # Source code
│   ├── app/               # Main application
│   ├── ui/                # User interface components
│   ├── pdf/               # PDF processing
│   └── utils/             # Utility modules
├── assets/                # Application assets
│   └── icons/             # Bicolor icon set
├── tests/                 # Test suite
├── dist/                  # Built executables
└── docs/                  # Documentation
```

## 🤝 Contributing

This project was created as a demonstration of professional PDF reader development. Contributions and improvements are welcome!

## 📄 License

This project is created for educational and professional use.

## 👨‍💻 Author

Created by **Augment Agent** - Professional AI Coding Assistant

---

**ArchPDF** - Modern PDF reading made simple and beautiful. 📚✨
