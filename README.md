# ArchPDF - Professional PDF Reader

A modern, standalone PDF reader application for Windows with a beautiful dark theme and professional bicolor icons.

## Features

- **Modern PDF Viewing**: High-quality PDF rendering with smooth navigation
- **Dark Theme**: Professional dark theme with bicolor icon set
- **Zoom Controls**: Zoom in/out, fit to window, actual size
- **Search Functionality**: Find text within PDF documents
- **Bookmarks Navigation**: Navigate PDF bookmarks when available
- **Recent Files**: Quick access to recently opened documents
- **Responsive Design**: Adapts to different screen sizes
- **Standalone Executable**: No installation required

## Technical Stack

- **Python 3.8+**: Core application language
- **tkinter/customtkinter**: Modern GUI framework
- **PyMuPDF (fitz)**: PDF processing and rendering
- **PyInstaller**: Standalone executable packaging
- **Pillow**: Image processing for icons and thumbnails

## Project Structure

```
archpdf_v2/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── build_exe.py           # PyInstaller build script
├── README.md              # This file
├── src/                   # Source code
│   ├── app/               # Main application
│   │   ├── main_window.py # Main application window
│   │   ├── config.py      # Configuration settings
│   │   └── settings.py    # Settings management
│   ├── ui/                # UI components
│   │   ├── toolbar.py     # Toolbar implementation
│   │   ├── menubar.py     # Menu bar implementation
│   │   ├── statusbar.py   # Status bar implementation
│   │   ├── sidebar.py     # Side panel implementation
│   │   └── theme.py       # Theme management
│   ├── pdf/               # PDF processing
│   │   ├── engine.py      # PDF engine
│   │   ├── viewer.py      # PDF viewer component
│   │   └── search.py      # Search functionality
│   └── utils/             # Utilities
│       ├── file_manager.py # File operations
│       ├── settings_manager.py # Settings persistence
│       └── icon_manager.py # Icon management
├── assets/                # Application assets
│   ├── icons/             # Bicolor icon set
│   └── themes/            # Theme definitions
└── dist/                  # Built executables (generated)
```

## Installation and Setup

### Development Setup

1. Clone or download the project
2. Install Python 3.8 or higher
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Run the application:
   ```bash
   python main.py
   ```

### Building Standalone Executable

1. Install PyInstaller:
   ```bash
   pip install pyinstaller
   ```
2. Run the build script:
   ```bash
   python build_exe.py
   ```
3. Find the executable in the `dist/` directory

## Usage

1. Launch ArchPDF
2. Use File > Open to select a PDF document
3. Navigate using toolbar buttons or keyboard shortcuts
4. Use zoom controls to adjust viewing size
5. Search text using Ctrl+F
6. Access recent files from the File menu

## Keyboard Shortcuts

- **Ctrl+O**: Open file
- **Ctrl+F**: Find text
- **Ctrl+Q**: Quit application
- **Page Up/Down**: Navigate pages
- **Ctrl++**: Zoom in
- **Ctrl+-**: Zoom out
- **Ctrl+0**: Actual size
- **F11**: Toggle fullscreen

## License

This project is created for educational and professional use.

## Author

Created by Augment Agent - Professional AI Coding Assistant
