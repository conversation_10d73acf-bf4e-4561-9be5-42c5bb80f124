"""
Test cases for PDF engine functionality.
"""

import unittest
import tempfile
import os
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pdf.engine import PDFEngine
from pdf.search import PDFSearchEngine

class TestPDFEngine(unittest.TestCase):
    """Test cases for PDFEngine class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.engine = PDFEngine()
        self.search_engine = PDFSearchEngine(self.engine)
        
        # Create a simple test PDF (would need actual PDF for real testing)
        self.test_pdf_path = None
        
    def tearDown(self):
        """Clean up after tests."""
        if self.engine.is_document_loaded():
            self.engine.close_document()
        
        if self.test_pdf_path and os.path.exists(self.test_pdf_path):
            os.remove(self.test_pdf_path)
    
    def test_engine_initialization(self):
        """Test PDF engine initialization."""
        self.assertIsNotNone(self.engine)
        self.assertFalse(self.engine.is_document_loaded())
        self.assertEqual(self.engine.page_count, 0)
        self.assertEqual(self.engine.current_page, 0)
    
    def test_load_nonexistent_file(self):
        """Test loading a non-existent file."""
        result = self.engine.load_document("nonexistent.pdf")
        self.assertFalse(result)
        self.assertFalse(self.engine.is_document_loaded())
    
    def test_load_invalid_file(self):
        """Test loading an invalid PDF file."""
        # Create a temporary invalid file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as f:
            f.write(b"This is not a PDF file")
            invalid_pdf_path = f.name
        
        try:
            result = self.engine.load_document(invalid_pdf_path)
            self.assertFalse(result)
            self.assertFalse(self.engine.is_document_loaded())
        finally:
            os.remove(invalid_pdf_path)
    
    def test_page_navigation(self):
        """Test page navigation methods."""
        # Without a loaded document
        self.assertFalse(self.engine.next_page())
        self.assertFalse(self.engine.previous_page())
        self.assertFalse(self.engine.set_current_page(5))
    
    def test_search_engine_initialization(self):
        """Test search engine initialization."""
        self.assertIsNotNone(self.search_engine)
        self.assertEqual(self.search_engine.get_result_count(), 0)
        self.assertEqual(self.search_engine.get_current_result_index(), 0)
    
    def test_search_without_document(self):
        """Test search without loaded document."""
        results = self.search_engine.search("test")
        self.assertEqual(len(results), 0)
    
    def test_search_empty_query(self):
        """Test search with empty query."""
        results = self.search_engine.search("")
        self.assertEqual(len(results), 0)
    
    def test_clear_search(self):
        """Test clearing search results."""
        self.search_engine.clear_search()
        self.assertEqual(self.search_engine.get_result_count(), 0)
        self.assertEqual(self.search_engine.last_search_query, "")

class TestPDFEngineWithDocument(unittest.TestCase):
    """Test cases that would require an actual PDF document."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.engine = PDFEngine()
        # Note: These tests would need actual PDF files to be meaningful
        
    def test_document_info_empty(self):
        """Test getting document info without loaded document."""
        info = self.engine.get_document_info()
        self.assertEqual(info, {})
    
    def test_bookmarks_empty(self):
        """Test getting bookmarks without loaded document."""
        bookmarks = self.engine.get_bookmarks()
        self.assertEqual(bookmarks, [])
    
    def test_page_text_empty(self):
        """Test getting page text without loaded document."""
        text = self.engine.get_page_text(0)
        self.assertEqual(text, "")
    
    def test_page_size_none(self):
        """Test getting page size without loaded document."""
        size = self.engine.get_page_size(0)
        self.assertIsNone(size)
    
    def test_page_image_none(self):
        """Test getting page image without loaded document."""
        image = self.engine.get_page_image(0)
        self.assertIsNone(image)

if __name__ == '__main__':
    unittest.main()
