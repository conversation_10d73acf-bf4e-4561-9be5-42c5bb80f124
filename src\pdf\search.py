"""
Search functionality for ArchPDF - Text search within PDF documents.
"""

import re
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class PDFSearchEngine:
    """Advanced search functionality for PDF documents."""
    
    def __init__(self, pdf_engine):
        """
        Initialize the search engine.
        
        Args:
            pdf_engine: Instance of PDFEngine
        """
        self.pdf_engine = pdf_engine
        self.last_search_query = ""
        self.search_results = []
        self.current_result_index = -1
    
    def search(self, query: str, case_sensitive: bool = False, 
               whole_words: bool = False, regex: bool = False) -> List[Dict[str, Any]]:
        """
        Search for text in the current document.
        
        Args:
            query: Search query
            case_sensitive: Whether search should be case sensitive
            whole_words: Whether to match whole words only
            regex: Whether query is a regular expression
            
        Returns:
            List of search results
        """
        if not self.pdf_engine.is_document_loaded() or not query:
            return []
        
        self.last_search_query = query
        self.search_results = []
        self.current_result_index = -1
        
        try:
            if regex:
                results = self._regex_search(query, case_sensitive)
            else:
                results = self._text_search(query, case_sensitive, whole_words)
            
            self.search_results = results
            if results:
                self.current_result_index = 0
                
            logger.info(f"Search for '{query}' found {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def _text_search(self, query: str, case_sensitive: bool, whole_words: bool) -> List[Dict[str, Any]]:
        """Perform text search."""
        results = []
        search_query = query if case_sensitive else query.lower()
        
        for page_num in range(self.pdf_engine.page_count):
            page_text = self.pdf_engine.get_page_text(page_num)
            if not page_text:
                continue
                
            search_text = page_text if case_sensitive else page_text.lower()
            
            if whole_words:
                # Use word boundaries for whole word search
                pattern = r'\b' + re.escape(search_query) + r'\b'
                matches = list(re.finditer(pattern, search_text))
            else:
                # Simple substring search
                start = 0
                matches = []
                while True:
                    pos = search_text.find(search_query, start)
                    if pos == -1:
                        break
                    matches.append(type('Match', (), {
                        'start': lambda: pos,
                        'end': lambda: pos + len(search_query),
                        'group': lambda: search_text[pos:pos + len(search_query)]
                    })())
                    start = pos + 1
            
            # Get bounding boxes for matches using PyMuPDF search
            if matches:
                fitz_results = self.pdf_engine.search_text(query, page_num)
                
                for i, match in enumerate(matches):
                    # Try to match with fitz results for bounding box
                    bbox = None
                    if i < len(fitz_results):
                        bbox = fitz_results[i]['bbox']
                    
                    # Get context around the match
                    context_start = max(0, match.start() - 50)
                    context_end = min(len(page_text), match.end() + 50)
                    context = page_text[context_start:context_end].strip()
                    
                    results.append({
                        'page': page_num,
                        'text': query,
                        'match_text': page_text[match.start():match.end()],
                        'context': context,
                        'position': match.start(),
                        'bbox': bbox
                    })
        
        return results
    
    def _regex_search(self, pattern: str, case_sensitive: bool) -> List[Dict[str, Any]]:
        """Perform regex search."""
        results = []
        flags = 0 if case_sensitive else re.IGNORECASE
        
        try:
            compiled_pattern = re.compile(pattern, flags)
        except re.error as e:
            logger.error(f"Invalid regex pattern '{pattern}': {e}")
            return []
        
        for page_num in range(self.pdf_engine.page_count):
            page_text = self.pdf_engine.get_page_text(page_num)
            if not page_text:
                continue
            
            matches = compiled_pattern.finditer(page_text)
            
            for match in matches:
                # Get context around the match
                context_start = max(0, match.start() - 50)
                context_end = min(len(page_text), match.end() + 50)
                context = page_text[context_start:context_end].strip()
                
                results.append({
                    'page': page_num,
                    'text': pattern,
                    'match_text': match.group(),
                    'context': context,
                    'position': match.start(),
                    'bbox': None  # Regex search doesn't provide bbox
                })
        
        return results
    
    def get_next_result(self) -> Optional[Dict[str, Any]]:
        """Get the next search result."""
        if not self.search_results:
            return None
            
        if self.current_result_index < len(self.search_results) - 1:
            self.current_result_index += 1
        else:
            self.current_result_index = 0  # Wrap around
            
        return self.search_results[self.current_result_index]
    
    def get_previous_result(self) -> Optional[Dict[str, Any]]:
        """Get the previous search result."""
        if not self.search_results:
            return None
            
        if self.current_result_index > 0:
            self.current_result_index -= 1
        else:
            self.current_result_index = len(self.search_results) - 1  # Wrap around
            
        return self.search_results[self.current_result_index]
    
    def get_current_result(self) -> Optional[Dict[str, Any]]:
        """Get the current search result."""
        if not self.search_results or self.current_result_index < 0:
            return None
        return self.search_results[self.current_result_index]
    
    def get_result_count(self) -> int:
        """Get the total number of search results."""
        return len(self.search_results)
    
    def get_current_result_index(self) -> int:
        """Get the current result index (1-based)."""
        return self.current_result_index + 1 if self.search_results else 0
    
    def clear_search(self):
        """Clear current search results."""
        self.search_results = []
        self.current_result_index = -1
        self.last_search_query = ""
    
    def highlight_text_on_page(self, page_num: int) -> List[Dict[str, Any]]:
        """
        Get all search result highlights for a specific page.
        
        Args:
            page_num: Page number to get highlights for
            
        Returns:
            List of highlight information for the page
        """
        highlights = []
        for result in self.search_results:
            if result['page'] == page_num and result.get('bbox'):
                highlights.append({
                    'bbox': result['bbox'],
                    'text': result['match_text'],
                    'is_current': result == self.get_current_result()
                })
        return highlights
